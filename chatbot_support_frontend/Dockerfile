FROM public.ecr.aws/docker/library/node:18-alpine as build

# Set Node.js environment to handle OpenSSL issues with newer Node versions
ENV NODE_OPTIONS=--openssl-legacy-provider

WORKDIR /app

COPY app/package*.json ./

RUN npm install

COPY app/ .

RUN npm run build

# Production stage
FROM public.ecr.aws/nginx/nginx:stable-alpine-perl

# Copy the build output to replace the default nginx contents
COPY --from=build /app/build /usr/share/nginx/html

# Copy the nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create a custom entrypoint script
RUN echo '#!/bin/sh' > /docker-entrypoint.d/40-custom-env.sh && \
    echo 'echo "window._env_ = {" > /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-custom-env.sh && \
    echo 'echo "  REACT_APP_API_URL: \"${REACT_APP_API_URL:-http://localhost:8000}\"," >> /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-custom-env.sh && \
    echo 'echo "  REACT_APP_ENV: \"${REACT_APP_ENV:-production}\"," >> /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-custom-env.sh && \
    echo 'echo "  REACT_APP_VERSION: \"${REACT_APP_VERSION:-1.0.0}\"," >> /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-custom-env.sh && \
    echo 'echo "};" >> /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-custom-env.sh && \
    echo 'exec "$@"' >> /docker-entrypoint.d/40-custom-env.sh && \
    chmod +x /docker-entrypoint.d/40-custom-env.sh

EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]
