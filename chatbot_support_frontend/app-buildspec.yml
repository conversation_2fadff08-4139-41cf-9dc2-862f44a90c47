version: 0.2

phases:
  pre_build:
    commands:
      - aws --version
      - output=$(aws codepipeline get-pipeline-execution --pipeline-name $PIPELINE_NAME --pipeline-execution-id $PIPELINE_EXECUTION_ID --query 'pipelineExecution.trigger.triggerDetail' | jq -r '. | fromjson | "\(.repositoryId) \(.branchName)"')
      - repositoryId=$(echo $output | awk '{print $1}')
      - triggeringRepo=$(echo "$repositoryId" | awk -F'/' '{print $NF}')
      - branchName=$(echo $output | awk '{print $2}')
      - echo "Triggering repository is $triggeringRepo"
      - echo "Checking if $triggeringRepo is in REPO_LIST($REPO_LIST)..."
      - >
        if echo ",$REPO_LIST," | grep -q ",$triggeringRepo,"; then
          echo "$triggeringRepo is in REPO_LIST. Proceeding with the build.";
          echo Logging in to Amazon ECR...
          aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com
          REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/$triggeringRepo
          echo repository uri $REPOSITORY_URI
          COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
          IMAGE_TAG=${COMMIT_HASH:=latest}
          if [ "$PRIMARY_SOURCE" != "${triggeringRepo}_source_output" ]; then
            resolved_path=$(eval echo "\$CODEBUILD_SRC_DIR_${triggeringRepo}_source_output")
            echo "Changing directory to $resolved_path"
            cd $resolved_path
          else
            echo "Keeping directory to Primary Source Artifact directory $PRIMARY_SOURCE"
          fi
          echo "Current directory is `pwd`"
          ls -la
          if [ -f "pre-build.sh" ]; then
            echo "Found pre-build.sh script. Executing..."
            chmod +x pre-build.sh
            ./pre-build.sh
          else
            echo "pre-build.sh script not found. Skipping..."
          fi
        else
          echo "$triggeringRepo is not in REPO_LIST. Exiting gracefully.";
          exit 0;
        fi
  build:
    commands:
      - echo Build started on `date`
      - >
        if echo ",$REPO_LIST," | grep -q ",$triggeringRepo,"; then
          echo "$triggeringRepo is in REPO_LIST. Proceeding with the build.";
          echo Building the Docker image...
          docker build -t $REPOSITORY_URI:latest .
          docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
        else
          echo "$triggeringRepo is not in REPO_LIST. Exiting gracefully.";
          exit 0;
        fi
      - echo Build completed on `date`
  post_build:
    commands:
      - echo Post Build started on `date`
      - >
        if echo ",$REPO_LIST," | grep -q ",$triggeringRepo,"; then
          echo "$triggeringRepo is in REPO_LIST. Proceeding with the build.";
          echo Pushing the Docker images...
          docker push $REPOSITORY_URI:latest
          docker push $REPOSITORY_URI:$IMAGE_TAG
          echo "Deploying the Docker image to ECS..."
          aws ecs update-service --cluster $ECS_CLUSTER_NAME --service $ECS_SERVICE_PREFIX-$triggeringRepo-service --force-new-deployment --deployment-configuration "maximumPercent=200,minimumHealthyPercent=50"
        else
          echo "$triggeringRepo is not in REPO_LIST. Exiting gracefully.";
          exit 0;
        fi      
      - echo Post Build completed on `date`
      - echo "Deployment completed on `date`"
