{"version": 3, "sources": ["index.css", "styles.css", "variables.css", "FixScrollbars.css", "theme-variables.css", "global-theme.css", "dark-mode-fix.css", "NotificationBell.css", "MarkdownRenderer.css", "ChatHistory.css", "UserChat.css", "AdminChat.css"], "names": [], "mappings": "AAAA,cAAc,CACd,oBAAoB,CACpB,mBAAmB,CAYnB,KACE,uEAEF,CAGA,MACE,iBACF,CAGA,oBACE,SAAU,CACV,UACF,CAEA,0BACE,kBACF,CAEA,gCACE,kBACF,CAEA,0BACE,eAAgB,CAChB,iBACF,CAEA,gCACE,eACF,CAEA,gCACE,kBACF,CAEA,sCACE,kBACF,CCpDA,MACE,uBAAwB,CACxB,uBAAwB,CACxB,oBAAqB,CACrB,oBAAqB,CACrB,kBAAmB,CACnB,cAAkB,CAIlB,sBACF,CAGA,MACE,oBAAqB,CACrB,oBAAqB,CACrB,kBAAmB,CACnB,iBAEF,CAEA,KACE,QAAS,CACT,mIAEY,CACZ,kCAAmC,CACnC,iCAAkC,CAClC,gCAAiC,CACjC,uBAAwB,CACxB,eACF,CAQA,SACE,WAAY,CACZ,+BAAgC,CAChC,0CAA2C,CAC3C,cAAe,CACf,YAAa,CACb,UAAW,CACX,6BACF,CAEA,gBACE,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAC9B,YAAa,CACb,2CACF,CAEA,aACE,cACF,CAEA,gBACE,eAAgB,CAChB,SAAU,CACV,QACF,CAEA,gBACE,mBACF,CAEA,eACE,YAAa,CACb,kBAAmB,CACnB,mBAAqB,CACrB,uBAAwB,CACxB,oBAAqB,CACrB,qBAAuB,CACvB,+BACF,CAEA,qBACE,gCACF,CAEA,sBACE,qCAAsC,CACtC,UACF,CAEA,mBACE,mBAAqB,CACrB,aAAc,CACd,cACF,CAEA,cAEE,iBAAkB,CAClB,aACF,CAEA,QACE,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAC9B,kBAAoB,CAEpB,2CAA4C,CAC5C,eAAgB,CAChB,KAAM,CACN,SAAU,CACV,WACF,CAGA,cATE,+BAeF,CANA,MAEE,mBAAqB,CACrB,mCAAwC,CACxC,cAAe,CACf,oBACF,CAEA,aACE,kBAAmB,CACnB,2CAA4C,CAC5C,mBACF,CAEA,YACE,iBAAkB,CAClB,eAAgB,CAChB,QACF,CAEA,KACE,mBAAoB,CACpB,kBAAmB,CACnB,sBAAuB,CACvB,kBAAoB,CACpB,qBAAuB,CACvB,eAAgB,CAChB,cAAe,CACf,+BAAiC,CACjC,WACF,CAEA,aACE,qCAAsC,CACtC,UACF,CAEA,mBACE,qCACF,CAEA,eACE,gCAAiC,CACjC,uBAAwB,CACxB,oCACF,CAEA,qBACE,oCACF,CAEA,YACE,kBACF,CAEA,YACE,aAAc,CACd,mBAAqB,CACrB,eACF,CAEA,YACE,UAAW,CACX,aAAe,CACf,oCAAqC,CACrC,qBAAuB,CACvB,+BAAgC,CAChC,uBACF,CAEA,kBACE,YAAa,CACb,iCAAkC,CAClC,wCACF,CAOA,MAEE,wBACF,CAEA,MACE,mBAAqB,CACrB,eAAgB,CAChB,2CACF,CAEA,GACE,eAAgB,CAChB,uBAAwB,CACxB,wBAAyB,CACzB,gBAAkB,CAClB,oBACF,CAGA,yBACE,SACE,2BACF,CAEA,cACE,uBACF,CAEA,cACE,aACF,CAEA,iBACE,aACF,CACF,CAEA,yBACE,iBACE,YACF,CACF,CAOA,wBACE,YACF,CAEA,yBACE,YACE,uBACF,CAEA,gBACE,6CACF,CACF,CAEA,yBAKE,sBACE,uBACF,CAEA,aACE,4BACF,CAEA,iBACE,4BACF,CAEA,oBACE,uCACF,CAEA,aACE,yBACF,CAEA,gBACE,6CACF,CACF,CAEA,0BACE,gBACE,6CACF,CAEA,gBACE,6CACF,CAEA,gBACE,6CACF,CACF,CChTA,MAKE,wBAQF,CAEA,YALE,qBAAsB,CACtB,uBAAwB,CACxB,uBAeF,CAZA,MACE,oBAAqB,CACrB,sBAAuB,CAMvB,sBAIF,CCnBA,yBAJE,iBASF,CALA,eAEE,UAGF,CAEA,cAIE,WAGF,CAGA,mBALE,cAAe,CACf,iBASF,CALA,KACE,QAAO,CACP,YAGF,CAGA,YACE,UAAW,CACX,cAAe,CACf,iBACF,CAGA,iBAEE,eAEF,CAGA,6BANE,UAAW,CAEX,cAOF,CAaA,4EACE,yBAA2B,CAC3B,cACF,CAEA,2BACE,yBAA2B,CAC3B,2BAA6B,CAC7B,cACF,CAGA,eACE,cAAe,CACf,iBACF,CAGA,MACE,UAEF,CAGA,4BAJE,cAMF,CCzFA,MAEE,yBAA6B,CAC7B,8BAA+B,CAC/B,6BAA8B,CAM9B,wBAAyB,CACzB,0BAA2B,CAE3B,wBAAyB,CACzB,0BAA2B,CAC3B,sBAAuB,CAGvB,uBAAwB,CACxB,4BAA6B,CAG7B,qBAAsB,CACtB,0BAA2B,CAG3B,uBAAwB,CACxB,4BAA6B,CAG7B,oBAAqB,CACrB,yBAA0B,CAG1B,qBAAsB,CACtB,0BAA2B,CAG3B,oBAAqB,CACrB,yBAA0B,CA6B1B,qBACF,CAEA,YA/DE,uBAAwB,CASxB,yBAA0B,CAI1B,uBAAwB,CAIxB,yBAA0B,CAI1B,sBAAuB,CAIvB,uBAAwB,CAIxB,sBAoGF,CAlEA,MAEE,4BAA6B,CAC7B,8BAA+B,CAC/B,6BAA8B,CAG9B,wBAAyB,CAGzB,wBAAyB,CACzB,0BAA2B,CAE3B,wBAAyB,CACzB,0BAA2B,CAC3B,sBAAuB,CAGvB,uBAAwB,CACxB,wDAA+B,CAG/B,qBAAsB,CACtB,sDAA6B,CAG7B,uBAAwB,CACxB,wDAA+B,CAG/B,oBAAqB,CACrB,qDAA4B,CAG5B,qBAAsB,CACtB,uDAA6B,CAG7B,oBAAqB,CACrB,qDAA4B,CA0B5B,qBACF,CClHA,8BACE,uBACF,CAEA,2BACE,kCACF,CAEA,6BACE,uBAAyB,CACzB,qCACF,CAEA,gDACE,kCACF,CAEA,kCACE,4CACF,CAEA,+BACE,kCAAoC,CACpC,uBACF,CAEA,8BACE,8BAAgC,CAChC,uBACF,CAEA,0BACE,uBACF,CAEA,6BACE,uBACF,CAEA,2CACE,8BACF,CAEA,8BACE,uBACF,CAEA,0CACE,uBACF,CAEA,8EACE,8BACF,CAGA,eACE,gBAAiB,CACjB,YAAa,CACb,oCACF,CAEA,yBACE,wBACF,CAGA,cACE,QAAO,CACP,YAAa,CACb,qBAAsB,CACtB,oCACF,CAEA,wBACE,wBACF,CAGA,SACE,oCACF,CAEA,mBACE,wBAAyB,CACzB,aAAc,CACd,0BACF,CAEA,0BACE,2BACF,CAEA,+BACE,aACF,CAEA,qCACE,wBACF,CAEA,sCACE,wBAAyB,CACzB,aACF,CAGA,QACE,oCACF,CAEA,kBACE,wBAAyB,CACzB,2BACF,CAGA,kCACE,aACF,CAEA,iCACE,wBAAyB,CACzB,oBACF,CAMA,4DACE,2BACF,CAEA,mCACE,wBACF,CAEA,oCACE,wBACF,CAEA,0CACE,wBACF,CAEA,8BACE,aACF,CAEA,gCACE,aACF,CAEA,6BACE,aACF,CAEA,oCACE,aACF,CAGA,0BACE,wBAAyB,CACzB,aACF,CAEA,iCACE,2BACF,CAEA,gCACE,aACF,CAEA,mCACE,aACF,CAGA,sCACE,wBACF,CAEA,+BACE,aACF,CAEA,mCACE,aACF,CAEA,+BACE,aACF,CAEA,+BACE,wBACF,CC7NA,UAEE,uBACF,CAMA,2DACE,kCACF,CAEA,mBACE,kCAAoC,CACpC,uBAAyB,CACzB,oCACF,CAEA,kBACE,kCAAoC,CACpC,qCAAuC,CACvC,qBACF,CAEA,yBACE,kCAAoC,CACpC,uBACF,CAEA,uBACE,uBACF,CAEA,oCACE,uBACF,CAEA,6BACE,kCACF,CAGA,iBAEE,uBACF,CAMA,gFACE,kCACF,CAEA,0BACE,kCAAoC,CACpC,uBAAyB,CACzB,oCACF,CAEA,yBACE,kCAAoC,CACpC,qCAAuC,CACvC,qBACF,CAEA,gCACE,kCAAoC,CACpC,uBACF,CAEA,8BACE,uBACF,CAEA,2CACE,uBACF,CAEA,oCACE,kCACF,CCnFA,6BACE,iBACF,CAGA,0BACE,eAAgB,CAChB,WAAY,CACZ,cAAe,CACf,iBAAkB,CAClB,aAAe,CACf,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,iBAAkB,CAClB,+BACF,CAEA,gCACE,gCACF,CAEA,sCACE,mCACF,CAGA,gDACE,gDACF,CAEA,4BACE,GACE,oCACF,CACA,GACE,oCACF,CACF,CAEA,sDACE,qDACF,CAEA,iCACE,GACE,oCACF,CACA,GACE,oCACF,CACF,CAGA,wBACE,YAAa,CACb,aAAc,CACd,yBACF,CAGA,8BACE,0CACF,CAEA,sBACE,GACE,kBAAmB,CACnB,yBACF,CACA,GACE,oBAAqB,CACrB,yBACF,CACF,CAGA,oBACE,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,wBAAyB,CACzB,UAAY,CACZ,gBAAkB,CAClB,eAAiB,CACjB,cAAe,CACf,WAAY,CACZ,sBAAyB,CACzB,oBAAqB,CACrB,YAAa,CACb,kBAAmB,CACnB,sBACF,CAGA,uBACE,iBAAkB,CAClB,QAAS,CACT,OAAQ,CACR,WAAY,CACZ,cAAe,CACf,gBAAiB,CACjB,qBAAuB,CACvB,mBAAqB,CACrB,uEAAiF,CACjF,UAAW,CACX,eAAgB,CAChB,gBAAkB,CAClB,YAAa,CACb,qBACF,CAEA,4BACE,wBAAyB,CACzB,sEACF,CAGA,qBACE,YAAa,CACb,6BAA8B,CAC9B,kBAAmB,CACnB,mBAAqB,CACrB,+BACF,CAEA,iDACE,2BACF,CAEA,wBACE,QAAS,CACT,cAAe,CACf,eAAgB,CAChB,yBACF,CAEA,sBACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,gBAAkB,CAClB,cAAe,CACf,oBAAuB,CACvB,oBAAsB,CACtB,+BACF,CAEA,4BACE,oCACF,CAEA,kDACE,aACF,CAEA,wDACE,oCACF,CAGA,mBACE,QAAO,CACP,eAAgB,CAChB,gBACF,CAGA,mBACE,YAAa,CACb,mBAAqB,CACrB,+BAAgC,CAChC,+BAAiC,CACjC,cACF,CAEA,+CACE,2BACF,CAEA,8BACE,kBACF,CAEA,yBACE,wBACF,CAEA,qDACE,wBACF,CAEA,0BACE,wBACF,CAEA,sDACE,wBACF,CAEA,gCACE,wBACF,CAEA,4DACE,wBACF,CAGA,mBACE,mBAAqB,CACrB,iBAAkB,CAClB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,aACF,CAGA,sBACE,QAAO,CACP,WACF,CAEA,oBACE,eAAgB,CAChB,iBAAmB,CACnB,oBAAsB,CACtB,yBACF,CAEA,sBACE,gBAAkB,CAClB,2BAA4B,CAC5B,oBAAsB,CACtB,qBACF,CAEA,mBACE,YAAa,CACb,eAAiB,CACjB,0BACF,CAEA,mBACE,kBACF,CAEA,sBACE,iBACF,CAGA,sBACE,YAAa,CACb,kBAAmB,CACnB,iBACF,CAEA,4BACE,eAAgB,CAChB,WAAY,CACZ,cAAe,CACf,cAAgB,CAChB,oBAAsB,CACtB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,+BAAiC,CACjC,kBACF,CAEA,kCACE,gCACF,CAEA,8DACE,mCACF,CAEA,0BACE,UAAW,CACX,WAAY,CACZ,2BACF,CAGA,0CAEE,YAAa,CACb,iBAAkB,CAClB,2BAA4B,CAC5B,iBACF,CCrSA,kBACE,wHACF,CAGA,8HAME,eAAgB,CAChB,mBAAqB,CACrB,eAAgB,CAChB,gBACF,CAEA,qBACE,gBACF,CAEA,qBACE,iBACF,CAEA,qBACE,kBACF,CAGA,oBACE,YAAa,CACb,oBACF,CAGA,0CAEE,mBAAoB,CACpB,YAAa,CACb,oBACF,CAEA,qBACE,oBACF,CAEA,uBACE,gBACF,CAEA,wBACE,iBACF,CAGA,sBACE,cAAgB,CAChB,aAAc,CACd,iBAAmB,CACnB,oBAAsB,CACtB,wBAAyB,CACzB,oBACF,CAEA,4BACE,wBACF,CAEA,uBACE,iBAAoB,CACpB,QAAS,CACT,eAAiB,CACjB,oBAAsB,CACtB,wBAAyB,CACzB,mEACF,CAEA,6BACE,wBACF,CAEA,2BACE,SAAU,CACV,wBACF,CAGA,6BACE,cAAe,CACf,aAAc,CACd,cAAe,CACf,gCAAkC,CAClC,aACF,CAEA,mCACE,yBAA0B,CAC1B,aACF,CAGA,wBACE,aAAc,CACd,UAAW,CACX,aAAc,CACd,gBAAiB,CACjB,wBAAyB,CACzB,oBACF,CAEA,2BACE,eAGF,CAEA,sDAJE,oBAAuB,CACvB,wBAMF,CAEA,kEAEE,oBACF,CAGA,oBACE,aAAc,CACd,oBACF,CAEA,0BACE,yBACF,CAEA,0BACE,aACF,CAGA,sBACE,cAAe,CACf,WAAY,CACZ,cACF,CAGA,qBACE,aAAe,CACf,SAAU,CACV,eAAgB,CAChB,wBAAyB,CACzB,QACF,CAEA,2BACE,wBACF,CChKA,MACE,qBAAsB,CACtB,8BAA+B,CAC/B,uBAAwB,CACxB,sBAAuB,CACvB,qBAAsB,CACtB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CACzB,iBAAqB,CACrB,sBAGF,CAEA,YAJE,sBAAuB,CACvB,sBAaF,CAVA,MACE,sBAAuB,CACvB,qBAAsB,CACtB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,sBAGF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,WAAY,CACZ,0BAA2B,CAC3B,eAAgB,CAChB,6CAA8C,CAC9C,oCAAqC,CACrC,0CACF,CAEA,uBACE,mCACF,CAGA,4BACE,yBAA0B,CAC1B,6BAA8B,CAC9B,eACF,CAEA,qBACE,YAAa,CACb,qBAAsB,CACtB,eAAY,CAAZ,UAAY,CACZ,YAAa,CACb,2CAA4C,CAC5C,kCACF,CAEA,oBACE,YAAa,CACb,6BAA8B,CAC9B,kBAAmB,CACnB,UACF,CAEA,uBACE,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,yBACF,CAEA,eACE,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,eAAgB,CAChB,WAAY,CACZ,cAAe,CACf,cAAgB,CAChB,oBAAsB,CACtB,2BACF,CAEA,qBACE,mCACF,CAEA,aACE,WAAY,CACZ,UACF,CAEA,iBACE,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,UAAW,CACX,oBAAuB,CACvB,qBAAuB,CACvB,oCAAqC,CACrC,UAAY,CACZ,WAAY,CACZ,cAAe,CACf,iBAAmB,CACnB,eAAgB,CAChB,+BACF,CAEA,uBACE,oCACF,CAEA,eACE,WAAY,CACZ,UAAW,CACX,mBACF,CAEA,mBACE,QAAO,CACP,eAAgB,CAChB,aAAe,CACf,oBAAqB,CACrB,4BACF,CAEA,sCACE,SACF,CAEA,4CACE,sBACF,CAEA,4CACE,sCAAuC,CACvC,iBAAkB,CAClB,UACF,CAEA,kDACE,oCAAqC,CACrC,SACF,CAGA,+BACE,8BAA+B,CAC/B,WAAY,CACZ,eACF,CAEA,oBACE,kBACF,CAEA,aACE,gBAAkB,CAClB,2BAA4B,CAC5B,aAAe,CACf,eAAgB,CAChB,wBAAyB,CACzB,oBACF,CAEA,mBACE,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAC9B,cAAgB,CAChB,qBAAuB,CACvB,cAAe,CACf,+BAAiC,CACjC,oBACF,CAEA,yBACE,mCACF,CAEA,0BACE,oCACF,CAEA,sBACE,YAAa,CACb,kBAAmB,CACnB,QACF,CAEA,mBACE,cAAe,CACf,aAAc,CACd,kBAAoB,CACpB,2BAA4B,CAC5B,aACF,CAEA,sBACE,eAAgB,CAChB,QACF,CAEA,oBACE,iBAAmB,CACnB,eAAgB,CAChB,kBAAmB,CACnB,eAAgB,CAChB,sBAAuB,CACvB,yBACF,CAEA,mBAGE,iBACF,CAEA,0CALE,gBAAkB,CAClB,2BAWF,CAPA,uBACE,YAAa,CACb,kBAAmB,CAGnB,oBAAsB,CACtB,iBACF,CAEA,eACE,cAAgB,CAChB,aAAe,CACf,mBAAqB,CACrB,2BACF,CAEA,eACE,gBAAkB,CAClB,2BACF,CAEA,kBACE,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CACnB,WAAY,CACZ,2BAA4B,CAC5B,iBAAmB,CACnB,iBAAkB,CAClB,YACF,CAEA,qBACE,YAAa,CACb,sBAAuB,CACvB,gBAAkB,CAClB,eAAgB,CAChB,QAAS,CACT,oCAAqC,CACrC,wCAAyC,CACzC,gBACF,CAEA,kBACE,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,SAAU,CACV,kBAAoB,CACpB,qBAAuB,CACvB,mCAAoC,CACpC,yBAA0B,CAC1B,oCAAqC,CACrC,cAAe,CACf,iBAAmB,CACnB,eAAgB,CAChB,uBAAyB,CACzB,oCACF,CAEA,wBACE,oCAAqC,CACrC,0BAA2B,CAC3B,mCACF,CAEA,yBACE,uBAAwB,CACxB,oCACF,CAEA,2BACE,UAAY,CACZ,kBAAmB,CACnB,cAAe,CACf,eACF,CAEA,gBACE,cAAgB,CAChB,aAAe,CACf,kBAAoB,CACpB,6BACF,CAEA,wCACE,yBACF,CAGA,eACE,eAAgB,CAChB,WAAY,CACZ,2BAA4B,CAC5B,UAAY,CACZ,cAAe,CACf,cAAgB,CAChB,oBAAsB,CACtB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,uBACF,CAEA,qBACE,SAAU,CACV,aAAc,CACd,mCACF,CAEA,aACE,UAAW,CACX,WACF,CAGA,gBACE,YAAa,CACb,kBAAmB,CACnB,cAAW,CAAX,SACF,CAEA,uBACE,eAAgB,CAChB,WAAY,CACZ,cAAe,CACf,cAAgB,CAChB,oBAAsB,CACtB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,uBACF,CAEA,YACE,aACF,CAEA,kBACE,mCACF,CAEA,WACE,2BACF,CAEA,iBACE,mCACF,CAEA,aACE,UAAW,CACX,WACF,CCnXA,gBAEE,qBAAsB,CACtB,WAAY,CACZ,UAEF,CAGA,2BARE,YAAa,CAIb,eAWF,CAPA,WACE,QAAO,CAEP,mBAAqB,CACrB,mCAAwC,CAExC,yBACF,CAGA,4BACE,gBACF,CAEA,uBACE,gBAAiB,CACjB,WAAY,CACZ,8BACF,CAGA,cACE,WAAY,CACZ,uBAAyB,CACzB,WAAY,CACZ,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,0CAA2C,CAC3C,oCACF,CAEA,uBACE,WAAY,CACZ,eACF,CAGA,0BACE,YAAa,CACb,eAAgB,CAChB,KAAM,CACN,eAAgB,CAChB,iBAAkB,CAClB,6BACF,CAGA,cACE,QAAO,CACP,YAAa,CACb,qBAAsB,CACtB,cAAe,CACf,WAAY,CACZ,WAAY,CACZ,eACF,CAGA,0BACE,WAAY,CACZ,8BAA+B,CAC/B,gBACF,CAGA,aACE,mBAAqB,CACrB,2CAA4C,CAC5C,YAAa,CACb,6BAA8B,CAC9B,kBACF,CAGA,eACE,QAAO,CACP,mBAAqB,CACrB,eAAgB,CAChB,oBAAqB,CACrB,8BAA+B,CAC/B,WACF,CAEA,kCACE,SACF,CAEA,wCACE,sBACF,CAEA,wCACE,sCAAuC,CACvC,iBAAkB,CAClB,UACF,CAEA,8CACE,oCAAqC,CACrC,SACF,CAGA,2BACE,eAAgB,CAChB,WAAY,CACZ,gBACF,CAGA,cACE,YAAa,CACb,qBAAsB,CACtB,eAAY,CAAZ,UAAY,CACZ,eAAgB,CAChB,oBACF,CAGA,aACE,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CACnB,WAAY,CACZ,YAAa,CACb,2BAA4B,CAC5B,iBACF,CAGA,sBACE,mBAAqB,CACrB,wCACF,CAEA,iBACE,YAAa,CACb,kBACF,CAEA,YACE,QAAO,CACP,mBAAqB,CAGrB,YAAa,CACb,iBACF,CAEA,8BANE,qBAAuB,CACvB,WAcF,CATA,kBACE,iBAAmB,CACnB,cAAgB,CAGhB,cAAe,CACf,YAAa,CACb,kBAAmB,CACnB,sBACF,CAGA,kBACE,oCAAqC,CACrC,yBACF,CAEA,wBACE,oCAAqC,CACrC,UACF,CAEA,iCACE,sCAAuC,CACvC,kBACF,CAGA,yBACE,cACE,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,kCAAmC,CACnC,WACF,CACF,CAEA,0BACE,WACE,yBACF,CAEA,eACE,8BACF,CACF,CAGA,uBACE,0BACF,CAEA,2BACE,8BACF,CCtNA,MACE,kBAAmB,CACnB,4EAAoF,CACpF,+BAAgC,CAChC,yBAA0B,CAE1B,qBAAyB,CACzB,2BAA4B,CAC5B,2BAA4B,CAC5B,8BAA+B,CAC/B,uBAAwB,CACxB,0BAA2B,CAC3B,gCAAiC,CACjC,6BAA8B,CAC9B,6BAAiC,CACjC,sCAAuC,CACvC,4BAA6B,CAC7B,8BAA+B,CAC/B,+BAAgC,CAChC,4BAA6B,CAC7B,+BAAgC,CAChC,gCAAiC,CACjC,8BACF,CAGA,YArBE,yBAyCF,CApBA,MACE,yBAA0B,CAE1B,wBAAyB,CACzB,2BAA4B,CAC5B,2BAA4B,CAC5B,8BAA+B,CAC/B,uBAAwB,CACxB,0BAA2B,CAC3B,gCAAiC,CACjC,6BAA8B,CAC9B,6BAAiC,CACjC,sCAAuC,CACvC,4BAA6B,CAC7B,8BAA+B,CAC/B,+BAAgC,CAChC,4BAA6B,CAC7B,+BAAgC,CAChC,gCAAiC,CACjC,8BACF,CAGA,uBACE,YAAa,CACb,qBAAsB,CACtB,WAAY,CACZ,UAAW,CACX,eAAgB,CAChB,kCAAmC,CACnC,iCACF,CAGA,kBAGE,gCAAiC,CACjC,6BAA8B,CAG9B,gBAAiB,CACjB,oCAIF,CAGA,uCAdE,QAAO,CACP,YAAa,CAGb,eAAgB,CAChB,WAAY,CAGZ,iCAAkC,CAClC,iBAAkB,CAClB,SAgBF,CAZA,qBAGE,qBAAsB,CACtB,cAAe,CACf,WAAY,CAGZ,kCAIF,CAGA,oBACE,kBAAoB,CACpB,iDAAkD,CAElD,6BAA8B,CAE9B,kCAAmC,CACnC,iCAAkC,CAClC,aAAc,CACd,SAAU,CACV,WAAY,CACZ,eACF,CAOA,oEAhBE,YAAa,CAEb,kBA2BF,CAbA,uBAGE,sBAAuB,CACvB,YAAa,CACb,aAAc,CACd,mBAAqB,CACrB,oCAAqC,CACrC,2BAA4B,CAC5B,WAAY,CACZ,cAAe,CACf,mBAAqB,CACrB,iCACF,CAEA,6BACE,mCAAoC,CACpC,qBACF,CAEA,kBACE,WAAY,CACZ,UAAW,CACX,iBAAkB,CAClB,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,oCAAqC,CACrC,kDAAqD,CACrD,kBAAoB,CACpB,uCACF,CAEA,uBACE,iBAAmB,CACnB,eAAgB,CAChB,UACF,CAEA,gBACE,YAAa,CACb,qBACF,CAEA,gBACE,eAAiB,CACjB,eAAgB,CAChB,yBAA0B,CAC1B,oBAAsB,CACtB,kBACF,CAEA,kBACE,YAAa,CACb,kBACF,CAEA,yBACE,YAAc,CACd,WAAa,CACb,iBAAkB,CAClB,kBAAoB,CACpB,iCACF,CAEA,+BACE,wBAAyB,CACzB,wCACF,CAEA,sCACE,wBAAyB,CACzB,wCAA6C,CAC7C,6BACF,CAEA,oBACE,eAAiB,CACjB,2BACF,CAGA,qBACE,YAAa,CACb,cAAW,CAAX,SAAW,CACX,cACF,CAEA,sBACE,YAAa,CACb,kBAAmB,CACnB,cAAW,CAAX,SAAW,CACX,oBAAuB,CACvB,mBAAqB,CACrB,oCAAqC,CACrC,2BAA4B,CAC5B,WAAY,CACZ,cAAe,CACf,iBAAmB,CACnB,eAAgB,CAChB,iCAAkC,CAClC,kBACF,CAEA,4BACE,mCAAoC,CACpC,0BAA2B,CAC3B,oCACF,CAEA,6BACE,uBACF,CAEA,0BACE,WAAY,CACZ,UACF,CAGA,sBACE,aAAc,CACd,mBAAqB,CACrB,eAAgB,CAChB,oBAAqB,CACrB,kCAAmC,CACnC,wKAEmF,CACnF,2BAA4B,CAC5B,iCAAkC,CAClC,iBAAkB,CAClB,SAAU,CACV,YACF,CAEA,yCACE,SACF,CAEA,+CACE,sBACF,CAEA,+CACE,sCAAuC,CACvC,iBAAkB,CAClB,UACF,CAEA,qDACE,oCAAqC,CACrC,SACF,CAEA,qBACE,YAAa,CACb,qBAAsB,CACtB,eAAY,CAAZ,UAAY,CACZ,eAAgB,CAChB,oBAAsB,CACtB,iBAAkB,CAClB,SACF,CAGA,0BACE,YAAa,CACb,cAAe,CACf,yBACF,CAEA,+BACE,wBACF,CAEA,8BACE,0BACF,CAEA,iCACE,sBACF,CAEA,uBACE,aAAc,CACd,oBAAsB,CACtB,cAAgB,CAChB,oCAAyC,CACzC,wBAAyB,CACzB,oBAAqB,CACrB,qBAAsB,CACtB,iCAAkC,CAClC,iBACF,CAEA,4BACE,uCAAwC,CACxC,+BAAgC,CAChC,iCACF,CAEA,2BACE,sCAAuC,CACvC,8BAA+B,CAC/B,gCACF,CAEA,8BACE,yCAA0C,CAC1C,iCAAkC,CAClC,aAAc,CACd,iBAAkB,CAClB,iBACF,CAEA,wBACE,kBAAoB,CACpB,eAAgB,CAChB,oBACF,CAEA,0BACE,gBAAkB,CAClB,gBAAkB,CAClB,iCAAkC,CAClC,gBAAiB,CACjB,UACF,CAGA,4BACE,eAAgB,CAChB,cAAgB,CAChB,mBAAqB,CACrB,0CAA2C,CAC3C,8CACF,CAEA,yBACE,iBAAmB,CACnB,eAAgB,CAChB,mBAAqB,CACrB,kCACF,CAEA,wBACE,YAAa,CACb,qBAAsB,CACtB,cAAW,CAAX,SACF,CAEA,uBACE,kBAAoB,CACpB,kCACF,CAEA,uBACE,iCAAkC,CAClC,oBAAqB,CACrB,eACF,CAEA,6BACE,yBACF,CAGA,yBACE,YAAa,CACb,0BAA2B,CAC3B,yBACF,CAEA,sBACE,kBAAmB,CACnB,mBAAqB,CACrB,sCAAuC,CACvC,8BAA+B,CAC/B,oCAAyC,CACzC,gCACF,CAEA,oBACE,YAAa,CACb,kBAAmB,CACnB,eAAY,CAAZ,UACF,CAEA,mBACE,WAAa,CACb,YAAc,CACd,iBAAkB,CAClB,6CACF,CAEA,+BACE,kCACF,CAEA,gCACE,kCACF,CAEA,gCACE,kCACF,CAGA,wBACE,YAAa,CACb,sBAAuB,CACvB,eAAgB,CAChB,yBACF,CAEA,sBACE,mBAAqB,CACrB,mBAAqB,CACrB,mCAAwC,CACxC,aAAc,CACd,iBAAmB,CACnB,YAAa,CACb,kBAAmB,CACnB,cAAW,CAAX,SACF,CAEA,mBACE,WAAY,CACZ,UACF,CAGA,6BACE,kBAAoB,CACpB,8CAA+C,CAC/C,kCAAmC,CACnC,iCAAkC,CAClC,iBAAkB,CAClB,SAAU,CACV,aACF,CAEA,wBACE,YAAa,CACb,oBAAqB,CACrB,iBACF,CAEA,mBACE,QAAO,CAEP,+BAAmB,CACnB,oBAAqB,CACrB,0CAA2C,CAC3C,YAAa,CACb,kBAAoB,CACpB,eAAgB,CAChB,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAClB,qCAAsC,CACtC,6BAA8B,CAC9B,iCACF,CAEA,yBACE,gCAAiC,CACjC,wCACF,CAEA,gCACE,mCAAoC,CACpC,iBACF,CAEA,yBACE,iBAAkB,CAClB,YAAc,CACd,YAAc,CACd,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,2CAA4C,CAC5C,mCAAoC,CACpC,WAAY,CACZ,cAAe,CACf,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,iCACF,CAEA,8CACE,qBAAsB,CACtB,mCACF,CAEA,kCACE,oDAAqD,CACrD,kBACF,CAEA,kBACE,cAAe,CACf,aAAc,CACd,uBACF,CAGA,kBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,kBACE,MAAW,uBAA0B,CACrC,IAAM,0BAA6B,CACrC,CAEA,iBACE,MAAW,SAAY,CACvB,IAAM,UAAc,CACtB,CAGA,4BACE,iBACF,CAEA,0BAEE,sBAAwB,CACxB,OAAQ,CACR,WAAY,CACZ,gBAAiB,CACjB,eAAgB,CAChB,kCAAmC,CACnC,mBAAqB,CACrB,uEAAiF,CACjF,WAAY,CACZ,0CAA2C,CAC3C,yBAA2B,CAE3B,iBACF,CAEA,wCACE,UAAW,CACX,WAAY,CACZ,gBAAiB,CACjB,iBACF,CAEA,6CACE,gBACF,CAGA,mCACE,yBAA0B,CAC1B,iBAAkB,CAClB,SAAU,CACV,cAAe,CACf,eACF,CAEA,8BACE,WACF,CAGA,kCACE,aAAc,CACd,eAAgB,CAChB,gBAAiB,CACjB,8BACF,CAGA,0BACE,sBACE,mBAAsB,CACtB,eACF,CAEA,0BACE,YAAc,CACd,WACF,CACF,CAEA,yBACE,qBACE,cAAW,CAAX,SACF,CAEA,sBACE,mBAAsB,CACtB,gBACF,CAEA,0BACE,aAAe,CACf,YACF,CACF,CAEA,yBACE,sBACE,cACF,CAEA,uBACE,aACF,CAEA,oBACE,oBACF,CAEA,gBACE,gBACF,CAEA,2BACE,YACF,CAEA,sBACE,aACF,CAEA,6BACE,cACF,CACF,CAEA,yBACE,qBACE,cAAW,CAAX,SACF,CAEA,sBACE,aACF,CAEA,kBACE,cAAe,CACf,aACF,CACF,CAEA,yBACE,qBACE,cAAW,CAAX,SACF,CAEA,sBACE,aACF,CAEA,2BACE,YACF,CAEA,0BACE,WAAY,CACZ,WACF,CACF,CAEA,yBACE,uBACE,aACF,CAEA,gBACE,iBACF,CAUA,uEACE,oBACF,CAEA,mBAEE,iCAAqB,CACrB,iBACF,CAEA,yBACE,aAAc,CACd,cACF,CAEA,kBACE,WAAY,CACZ,UACF,CACF", "file": "main.7bfbf160.chunk.css", "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Additional custom styles can be added here */\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Dark mode styles */\n.dark {\n  color-scheme: dark;\n}\n\n/* Scrollbar styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n.dark ::-webkit-scrollbar-track {\n  background: #374151;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #888;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #555;\n}\n\n.dark ::-webkit-scrollbar-thumb {\n  background: #4B5563;\n}\n\n.dark ::-webkit-scrollbar-thumb:hover {\n  background: #6B7280;\n}\n", "/* Basic styles without Tailwind */\n:root {\n  --primary-color: #3B82F6;\n  --primary-hover: #2563EB;\n  --text-color: #1F2937;\n  --text-light: #6B7280;\n  --bg-color: #F3F4F6;\n  --bg-card: #FFFFFF;\n  --border-color: #E5E7EB;\n  --success-color: #10B981;\n  --warning-color: #F59E0B;\n  --danger-color: #EF4444;\n}\n\n/* Dark mode variables */\n.dark {\n  --text-color: #F9FAFB;\n  --text-light: #9CA3AF;\n  --bg-color: #111827;\n  --bg-card: #1F2937;\n  --border-color: #374151;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: var(--bg-color);\n  color: var(--text-color);\n  overflow: hidden; /* Prevent system scrollbar */\n}\n\n/* Layout */\n.app-container {\n  display: flex;\n  min-height: 100vh;\n}\n\n.sidebar {\n  width: 250px;\n  background-color: var(--bg-card);\n  border-right: 1px solid var(--border-color);\n  position: fixed;\n  height: 100vh;\n  z-index: 10;\n  transition: transform 0.3s ease;\n}\n\n.sidebar-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1rem;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.sidebar-nav {\n  padding: 1rem 0;\n}\n\n.sidebar-nav ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.sidebar-nav li {\n  margin-bottom: 0.5rem;\n}\n\n.sidebar-nav a {\n  display: flex;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  color: var(--text-color);\n  text-decoration: none;\n  border-radius: 0.375rem;\n  transition: background-color 0.2s;\n}\n\n.sidebar-nav a:hover {\n  background-color: var(--bg-color);\n}\n\n.sidebar-nav a.active {\n  background-color: var(--primary-color);\n  color: white;\n}\n\n.sidebar-nav a svg {\n  margin-right: 0.75rem;\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.main-content {\n  flex: 1;\n  margin-left: 250px;\n  padding: 0.5rem; /* Reduced padding */\n}\n\n.topbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 1rem;\n  background-color: var(--bg-card);\n  border-bottom: 1px solid var(--border-color);\n  position: sticky;\n  top: 0;\n  z-index: 5;\n  height: 50px; /* Standard height */\n}\n\n/* Components */\n.card {\n  background-color: var(--bg-card);\n  border-radius: 0.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.card-header {\n  margin-bottom: 1rem;\n  border-bottom: 1px solid var(--border-color);\n  padding-bottom: 1rem;\n}\n\n.card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  border: none;\n}\n\n.btn-primary {\n  background-color: var(--primary-color);\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: var(--primary-hover);\n}\n\n.btn-secondary {\n  background-color: var(--bg-color);\n  color: var(--text-color);\n  border: 1px solid var(--border-color);\n}\n\n.btn-secondary:hover {\n  background-color: var(--border-color);\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n}\n\n.form-input {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid var(--border-color);\n  border-radius: 0.375rem;\n  background-color: var(--bg-card);\n  color: var(--text-color);\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);\n}\n\n/* Table styles */\n.table-container {\n  overflow-x: auto;\n}\n\ntable {\n  width: 100%;\n  border-collapse: collapse;\n}\n\nth, td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid var(--border-color);\n}\n\nth {\n  font-weight: 600;\n  color: var(--text-light);\n  text-transform: uppercase;\n  font-size: 0.75rem;\n  letter-spacing: 0.05em;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .sidebar {\n    transform: translateX(-100%);\n  }\n\n  .sidebar.open {\n    transform: translateX(0);\n  }\n\n  .main-content {\n    margin-left: 0;\n  }\n\n  .mobile-menu-btn {\n    display: block;\n  }\n}\n\n@media (min-width: 769px) {\n  .mobile-menu-btn {\n    display: none;\n  }\n}\n\n/* Visibility classes */\n.sm-visible {\n  display: none;\n}\n\n.md-visible {\n  display: none;\n}\n\n@media (min-width: 640px) {\n  .sm-visible {\n    display: block !important;\n  }\n\n  .sm-grid-cols-2 {\n    grid-template-columns: repeat(2, 1fr) !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .md-visible {\n    display: block !important;\n  }\n\n  .md-block {\n    display: block !important;\n  }\n\n  .md-flex-row {\n    flex-direction: row !important;\n  }\n\n  .md-items-center {\n    align-items: center !important;\n  }\n\n  .md-justify-between {\n    justify-content: space-between !important;\n  }\n\n  .md-max-w-md {\n    max-width: 28rem !important;\n  }\n\n  .md-grid-cols-2 {\n    grid-template-columns: repeat(2, 1fr) !important;\n  }\n}\n\n@media (min-width: 1024px) {\n  .lg-grid-cols-2 {\n    grid-template-columns: repeat(2, 1fr) !important;\n  }\n\n  .lg-grid-cols-3 {\n    grid-template-columns: repeat(3, 1fr) !important;\n  }\n\n  .lg-grid-cols-4 {\n    grid-template-columns: repeat(4, 1fr) !important;\n  }\n}\n", ":root {\n  /* Colors */\n  --bg-primary: #FFFFFF;\n  --bg-secondary: #F9FAFB;\n  --text-primary: #111827;\n  --text-secondary: #4B5563;\n  --text-tertiary: #9CA3AF;\n  --accent-color: #3B82F6;\n  --accent-hover: #2563EB;\n  --border-color: #E5E7EB;\n  --error-color: #EF4444;\n  --success-color: #10B981;\n  --warning-color: #F59E0B;\n}\n\n.dark {\n  --bg-primary: #111827;\n  --bg-secondary: #1F2937;\n  --text-primary: #F9FAFB;\n  --text-secondary: #D1D5DB;\n  --text-tertiary: #9CA3AF;\n  --accent-color: #3B82F6;\n  --accent-hover: #2563EB;\n  --border-color: #374151;\n  --error-color: #EF4444;\n  --success-color: #10B981;\n  --warning-color: #F59E0B;\n}\n", "/* Fix for scrollbar issues in admin interface */\n\n/* Global fixes */\nhtml, body {\n  overflow-x: hidden; /* Prevent horizontal scrolling at the document level */\n}\n\n/* Admin layout fixes */\n.app-container {\n  display: flex;\n  width: 100%;\n  min-height: 100vh;\n  overflow-x: hidden; /* Prevent horizontal scrolling */\n}\n\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; /* Important: allows flex items to shrink below content size */\n  max-width: 100%; /* Ensure content doesn't exceed viewport width */\n  overflow-x: hidden; /* Prevent horizontal scrolling */\n}\n\n/* Main content area */\nmain {\n  flex: 1;\n  padding: 1rem;\n  overflow-x: hidden; /* Prevent horizontal scrolling */\n  max-width: 100%; /* Ensure content doesn't exceed viewport width */\n}\n\n/* Admin page fixes */\n.admin-page {\n  width: 100%;\n  max-width: 100%;\n  overflow-x: hidden; /* Prevent horizontal scrolling */\n}\n\n/* Table container fixes */\n.table-container {\n  width: 100%;\n  overflow-x: auto; /* Allow horizontal scrolling only within tables */\n  max-width: 100%; /* Ensure content doesn't exceed viewport width */\n}\n\n/* Search bar fixes */\n.search-bar {\n  max-width: 100%; /* Ensure search bar doesn't exceed viewport width */\n  width: 100%;\n}\n\n/* Chat container fixes */\n.chat-container.admin-page {\n  overflow: hidden !important; /* Override any other styles */\n  max-width: 100%;\n}\n\n.admin-page .chat-main {\n  overflow: hidden !important; /* Override any other styles */\n  max-width: 100%;\n}\n\n.admin-page .chat-content {\n  overflow: hidden !important; /* Override any other styles */\n  max-width: 100%;\n}\n\n.admin-page .chat-messages {\n  overflow-y: auto !important; /* Allow vertical scrolling */\n  overflow-x: hidden !important; /* Prevent horizontal scrolling */\n  max-width: 100%;\n}\n\n/* Fix for modals */\n.modal-content {\n  max-width: 100%;\n  overflow-x: hidden;\n}\n\n/* Fix for tables */\ntable {\n  width: 100%;\n  max-width: 100%;\n}\n\n/* Fix for inputs and form elements */\ninput, select, textarea {\n  max-width: 100%;\n}\n", ":root {\n  /* Light theme colors */\n  --background-primary: #FFFFFF;\n  --background-secondary: #F9FAFB;\n  --background-tertiary: #F3F4F6;\n\n  --text-primary: #111827;\n  --text-secondary: #6B7280;\n  --text-tertiary: #9CA3AF;\n\n  --border-primary: #E5E7EB;\n  --border-secondary: #D1D5DB;\n\n  --accent-primary: #2563EB;\n  --accent-secondary: #3B82F6;\n  --accent-light: #EFF6FF;\n\n  --success-primary: #059669;\n  --success-light: #D1FAE5;\n  --success-background: #D1FAE5;\n\n  --error-primary: #DC2626;\n  --error-light: #FEE2E2;\n  --error-background: #FEE2E2;\n\n  --warning-primary: #D97706;\n  --warning-light: #FEF3C7;\n  --warning-background: #FEF3C7;\n\n  --info-primary: #2563EB;\n  --info-light: #DBEAFE;\n  --info-background: #DBEAFE;\n\n  --admin-primary: #7E22CE;\n  --admin-light: #F3E8FF;\n  --admin-background: #F3E8FF;\n\n  --user-primary: #1D4ED8;\n  --user-light: #DBEAFE;\n  --user-background: #DBEAFE;\n\n  /* Chat specific variables */\n  --chat-radius: 1rem;\n  --chat-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --chat-transition: all 0.3s ease;\n  --user-message-bg: #3B82F6;\n  --user-message-color: #FFFFFF;\n  --bot-message-bg: #FFFFFF;\n  --bot-message-color: #111827;\n  --system-message-bg: #F3F4F6;\n  --system-message-color: #4B5563;\n  --chat-input-bg: #F3F4F6;\n  --chat-input-color: #111827;\n  --chat-input-placeholder: #9CA3AF;\n  --chat-send-button-bg: #3B82F6;\n  --chat-send-button-color: #FFFFFF;\n  --chat-send-button-disabled-bg: #9CA3AF;\n  --chat-header-border: #E5E7EB;\n  --chat-timestamp-color: #9CA3AF;\n  --chat-typing-dot-color: #9CA3AF;\n  --chat-references-bg: #F3F4F6;\n  --chat-references-color: #4B5563;\n  --chat-references-border: #E5E7EB;\n  --chat-references-link: #2563EB;\n  --hover-color: #F3F4F6;\n  --active-color: #E5E7EB;\n  --bg-primary: #FFFFFF;\n  --bg-secondary: #F9FAFB;\n  --bg-tertiary: #F3F4F6;\n}\n\n.dark {\n  /* Dark theme colors */\n  --background-primary: #1F2937;\n  --background-secondary: #374151;\n  --background-tertiary: #4B5563;\n\n  --text-primary: #F9FAFB;\n  --text-secondary: #D1D5DB;\n  --text-tertiary: #9CA3AF;\n\n  --border-primary: #4B5563;\n  --border-secondary: #6B7280;\n\n  --accent-primary: #3B82F6;\n  --accent-secondary: #60A5FA;\n  --accent-light: #1E3A8A;\n\n  --success-primary: #059669;\n  --success-light: #A7F3D0;\n  --success-background: #05966930;\n\n  --error-primary: #DC2626;\n  --error-light: #FCA5A5;\n  --error-background: #DC262630;\n\n  --warning-primary: #D97706;\n  --warning-light: #FCD34D;\n  --warning-background: #D9770630;\n\n  --info-primary: #2563EB;\n  --info-light: #BFDBFE;\n  --info-background: #2563EB30;\n\n  --admin-primary: #7E22CE;\n  --admin-light: #E9D5FF;\n  --admin-background: #7E22CE30;\n\n  --user-primary: #1D4ED8;\n  --user-light: #BFDBFE;\n  --user-background: #1D4ED830;\n\n  /* Chat specific variables - Dark mode */\n  --user-message-bg: #2563EB;\n  --user-message-color: #FFFFFF;\n  --bot-message-bg: #374151;\n  --bot-message-color: #F9FAFB;\n  --system-message-bg: #4B5563;\n  --system-message-color: #F9FAFB;\n  --chat-input-bg: #374151;\n  --chat-input-color: #F9FAFB;\n  --chat-input-placeholder: #9CA3AF;\n  --chat-send-button-bg: #2563EB;\n  --chat-send-button-color: #FFFFFF;\n  --chat-send-button-disabled-bg: #4B5563;\n  --chat-header-border: #4B5563;\n  --chat-timestamp-color: #9CA3AF;\n  --chat-typing-dot-color: #9CA3AF;\n  --chat-references-bg: #4B5563;\n  --chat-references-color: #F9FAFB;\n  --chat-references-border: #6B7280;\n  --chat-references-link: #60A5FA;\n  --hover-color: #374151;\n  --active-color: #4B5563;\n  --bg-primary: #1F2937;\n  --bg-secondary: #111827;\n  --bg-tertiary: #374151;\n}\n", "/* Global theme styles */\nbody.dark {\n  background-color: #1F2937 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiPaper-root {\n  background-color: #374151 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiTab-root {\n  color: #D1D5DB !important;\n}\n\nbody.dark .MuiTab-root.Mui-selected {\n  color: #60A5FA !important;\n}\n\nbody.dark .MuiTabs-indicator {\n  background-color: #60A5FA !important;\n}\n\nbody.dark .MuiTypography-root {\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiDivider-root {\n  background-color: #4B5563 !important;\n}\n\nbody.dark .MuiTableCell-root {\n  color: #F9FAFB !important;\n  border-bottom-color: #4B5563 !important;\n}\n\nbody.dark .MuiTableHead-root .MuiTableCell-root {\n  background-color: #374151 !important;\n}\n\nbody.dark .MuiTableRow-root:hover {\n  background-color: rgba(75, 85, 99, 0.2) !important;\n}\n\nbody.dark .MuiButton-contained {\n  background-color: #3B82F6 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiButton-outlined {\n  border-color: #4B5563 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiButton-text {\n  color: #60A5FA !important;\n}\n\nbody.dark .MuiInputBase-root {\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiOutlinedInput-notchedOutline {\n  border-color: #4B5563 !important;\n}\n\nbody.dark .MuiInputLabel-root {\n  color: #9CA3AF !important;\n}\n\nbody.dark .MuiInputLabel-root.Mui-focused {\n  color: #60A5FA !important;\n}\n\nbody.dark .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {\n  border-color: #60A5FA !important;\n}\n\n/* App container */\n.app-container {\n  min-height: 100vh;\n  display: flex;\n  transition: background-color 0.3s ease;\n}\n\nbody.dark .app-container {\n  background-color: #1F2937;\n}\n\n/* Main content */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  transition: background-color 0.3s ease;\n}\n\nbody.dark .main-content {\n  background-color: #1F2937;\n}\n\n/* Sidebar */\n.sidebar {\n  transition: background-color 0.3s ease;\n}\n\nbody.dark .sidebar {\n  background-color: #111827;\n  color: #F9FAFB;\n  border-right-color: #374151;\n}\n\nbody.dark .sidebar-header {\n  border-bottom-color: #374151;\n}\n\nbody.dark .sidebar-nav ul li a {\n  color: #D1D5DB;\n}\n\nbody.dark .sidebar-nav ul li a:hover {\n  background-color: #374151;\n}\n\nbody.dark .sidebar-nav ul li a.active {\n  background-color: #3B82F6;\n  color: #F9FAFB;\n}\n\n/* Topbar */\n.topbar {\n  transition: background-color 0.3s ease;\n}\n\nbody.dark .topbar {\n  background-color: #111827;\n  border-bottom-color: #374151;\n}\n\n/* Notification bell */\nbody.dark .notification-bell-icon {\n  color: #F9FAFB;\n}\n\nbody.dark .notification-dropdown {\n  background-color: #1F2937;\n  border-color: #374151;\n}\n\nbody.dark .notification-header {\n  border-bottom-color: #374151;\n}\n\nbody.dark .notification-item {\n  border-bottom-color: #374151;\n}\n\nbody.dark .notification-item:hover {\n  background-color: #2D3748;\n}\n\nbody.dark .notification-item.unread {\n  background-color: #1E3A8A;\n}\n\nbody.dark .notification-item.unread:hover {\n  background-color: #1E40AF;\n}\n\nbody.dark .notification-title {\n  color: #F9FAFB;\n}\n\nbody.dark .notification-message {\n  color: #D1D5DB;\n}\n\nbody.dark .notification-meta {\n  color: #9CA3AF;\n}\n\nbody.dark .notification-action-icon {\n  color: #D1D5DB;\n}\n\n/* Dashboard components */\nbody.dark .dashboard-card {\n  background-color: #374151;\n  color: #F9FAFB;\n}\n\nbody.dark .dashboard-card-header {\n  border-bottom-color: #4B5563;\n}\n\nbody.dark .dashboard-card-title {\n  color: #F9FAFB;\n}\n\nbody.dark .dashboard-card-subtitle {\n  color: #D1D5DB;\n}\n\n/* Fix for Material UI components */\nbody.dark .MuiPaper-root.MuiCard-root {\n  background-color: #374151;\n}\n\nbody.dark .MuiCardHeader-title {\n  color: #F9FAFB;\n}\n\nbody.dark .MuiCardHeader-subheader {\n  color: #D1D5DB;\n}\n\nbody.dark .MuiCardContent-root {\n  color: #F9FAFB;\n}\n\nbody.dark .MuiCardActions-root {\n  border-top-color: #4B5563;\n}\n", "/* Dark mode fix */\nbody.dark {\n  background-color: #1F2937 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .app-container {\n  background-color: #1F2937 !important;\n}\n\nbody.dark .main-content {\n  background-color: #1F2937 !important;\n}\n\nbody.dark .sidebar {\n  background-color: #111827 !important;\n  color: #F9FAFB !important;\n  border-right-color: #374151 !important;\n}\n\nbody.dark .topbar {\n  background-color: #111827 !important;\n  border-bottom-color: #374151 !important;\n  height: 50px !important; /* Ensure consistent height in dark mode */\n}\n\nbody.dark .MuiPaper-root {\n  background-color: #374151 !important;\n  color: #F9FAFB !important;\n}\n\nbody.dark .MuiTab-root {\n  color: #D1D5DB !important;\n}\n\nbody.dark .MuiTab-root.Mui-selected {\n  color: #60A5FA !important;\n}\n\nbody.dark .MuiTabs-indicator {\n  background-color: #60A5FA !important;\n}\n\n/* Force dark mode for testing */\n.force-dark-mode {\n  background-color: #1F2937 !important;\n  color: #F9FAFB !important;\n}\n\n.force-dark-mode .app-container {\n  background-color: #1F2937 !important;\n}\n\n.force-dark-mode .main-content {\n  background-color: #1F2937 !important;\n}\n\n.force-dark-mode .sidebar {\n  background-color: #111827 !important;\n  color: #F9FAFB !important;\n  border-right-color: #374151 !important;\n}\n\n.force-dark-mode .topbar {\n  background-color: #111827 !important;\n  border-bottom-color: #374151 !important;\n  height: 50px !important; /* Ensure consistent height in forced dark mode */\n}\n\n.force-dark-mode .MuiPaper-root {\n  background-color: #374151 !important;\n  color: #F9FAFB !important;\n}\n\n.force-dark-mode .MuiTab-root {\n  color: #D1D5DB !important;\n}\n\n.force-dark-mode .MuiTab-root.Mui-selected {\n  color: #60A5FA !important;\n}\n\n.force-dark-mode .MuiTabs-indicator {\n  background-color: #60A5FA !important;\n}\n", "/* Notification Bell Container */\n.notification-bell-container {\n  position: relative;\n}\n\n/* Notification Bell Button */\n.notification-bell-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  position: relative;\n  padding: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background-color 0.2s;\n}\n\n.notification-bell-button:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.dark .notification-bell-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n/* New notifications indicator */\n.notification-bell-button.has-new-notifications {\n  animation: highlight-button 2s infinite alternate;\n}\n\n@keyframes highlight-button {\n  0% {\n    background-color: rgba(59, 130, 246, 0.1);\n  }\n  100% {\n    background-color: rgba(59, 130, 246, 0.2);\n  }\n}\n\n.dark .notification-bell-button.has-new-notifications {\n  animation: highlight-button-dark 2s infinite alternate;\n}\n\n@keyframes highlight-button-dark {\n  0% {\n    background-color: rgba(96, 165, 250, 0.1);\n  }\n  100% {\n    background-color: rgba(96, 165, 250, 0.2);\n  }\n}\n\n/* Notification Bell Icon */\n.notification-bell-icon {\n  width: 1.5rem;\n  height: 1.5rem;\n  color: var(--text-primary);\n}\n\n/* Pulsing animation for new notifications */\n.notification-bell-icon.pulse {\n  animation: pulse-icon 1s infinite alternate;\n}\n\n@keyframes pulse-icon {\n  0% {\n    transform: scale(1);\n    color: var(--text-primary);\n  }\n  100% {\n    transform: scale(1.1);\n    color: var(--accent-color);\n  }\n}\n\n/* Notification Badge */\n.notification-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background-color: #ef4444;\n  color: white;\n  font-size: 0.75rem;\n  font-weight: bold;\n  min-width: 1rem;\n  height: 1rem;\n  padding: 0.125rem 0.25rem;\n  border-radius: 9999px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Notification Dropdown */\n.notification-dropdown {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 350px;\n  max-width: 90vw;\n  max-height: 500px;\n  background-color: white;\n  border-radius: 0.5rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  z-index: 50;\n  overflow: hidden;\n  margin-top: 0.5rem;\n  display: flex;\n  flex-direction: column;\n}\n\n.notification-dropdown.dark {\n  background-color: #1f2937;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.3);\n}\n\n/* Notification Header */\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.notification-dropdown.dark .notification-header {\n  border-bottom-color: #374151;\n}\n\n.notification-header h3 {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.mark-all-read-button {\n  background: none;\n  border: none;\n  color: #3b82f6;\n  font-size: 0.75rem;\n  cursor: pointer;\n  padding: 0.25rem 0.5rem;\n  border-radius: 0.25rem;\n  transition: background-color 0.2s;\n}\n\n.mark-all-read-button:hover {\n  background-color: rgba(59, 130, 246, 0.1);\n}\n\n.notification-dropdown.dark .mark-all-read-button {\n  color: #60a5fa;\n}\n\n.notification-dropdown.dark .mark-all-read-button:hover {\n  background-color: rgba(96, 165, 250, 0.1);\n}\n\n/* Notification List */\n.notification-list {\n  flex: 1;\n  overflow-y: auto;\n  max-height: 400px;\n}\n\n/* Notification Item */\n.notification-item {\n  display: flex;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid #e5e7eb;\n  transition: background-color 0.2s;\n  cursor: pointer;\n}\n\n.notification-dropdown.dark .notification-item {\n  border-bottom-color: #374151;\n}\n\n.notification-item:last-child {\n  border-bottom: none;\n}\n\n.notification-item:hover {\n  background-color: #f9fafb;\n}\n\n.notification-dropdown.dark .notification-item:hover {\n  background-color: #2d3748;\n}\n\n.notification-item.unread {\n  background-color: #f0f9ff;\n}\n\n.notification-dropdown.dark .notification-item.unread {\n  background-color: #1e3a8a;\n}\n\n.notification-item.unread:hover {\n  background-color: #e0f2fe;\n}\n\n.notification-dropdown.dark .notification-item.unread:hover {\n  background-color: #1e40af;\n}\n\n/* Notification Icon */\n.notification-icon {\n  margin-right: 0.75rem;\n  font-size: 1.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n/* Notification Content */\n.notification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.notification-title {\n  font-weight: 600;\n  font-size: 0.875rem;\n  margin-bottom: 0.25rem;\n  color: var(--text-primary);\n}\n\n.notification-message {\n  font-size: 0.75rem;\n  color: var(--text-secondary);\n  margin-bottom: 0.25rem;\n  word-break: break-word;\n}\n\n.notification-meta {\n  display: flex;\n  font-size: 0.7rem;\n  color: var(--text-tertiary);\n}\n\n.notification-time {\n  margin-right: 0.5rem;\n}\n\n.notification-creator {\n  font-style: italic;\n}\n\n/* Notification Actions */\n.notification-actions {\n  display: flex;\n  align-items: center;\n  margin-left: 0.5rem;\n}\n\n.notification-action-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 0.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s;\n  margin-left: 0.25rem;\n}\n\n.notification-action-button:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.notification-dropdown.dark .notification-action-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.notification-action-icon {\n  width: 1rem;\n  height: 1rem;\n  color: var(--text-secondary);\n}\n\n/* Loading and Empty States */\n.notification-loading,\n.notification-empty {\n  padding: 1rem;\n  text-align: center;\n  color: var(--text-secondary);\n  font-size: 0.875rem;\n}\n", "/* Markdown content styling */\n.markdown-content {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n/* Headers */\n.markdown-content h1,\n.markdown-content h2,\n.markdown-content h3,\n.markdown-content h4,\n.markdown-content h5,\n.markdown-content h6 {\n  margin-top: 1rem;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  line-height: 1.25;\n}\n\n.markdown-content h1 {\n  font-size: 1.5rem;\n}\n\n.markdown-content h2 {\n  font-size: 1.25rem;\n}\n\n.markdown-content h3 {\n  font-size: 1.125rem;\n}\n\n/* Paragraphs */\n.markdown-content p {\n  margin-top: 0;\n  margin-bottom: 0.75rem;\n}\n\n/* Lists */\n.markdown-content ul,\n.markdown-content ol {\n  padding-left: 1.5rem;\n  margin-top: 0;\n  margin-bottom: 0.75rem;\n}\n\n.markdown-content li {\n  margin-bottom: 0.25rem;\n}\n\n.markdown-content li > p {\n  margin-top: 0.5rem;\n}\n\n.markdown-content li + li {\n  margin-top: 0.25rem;\n}\n\n/* Code blocks */\n.markdown-content pre {\n  padding: 0.75rem;\n  overflow: auto;\n  font-size: 0.875rem;\n  border-radius: 0.25rem;\n  background-color: #f3f4f6;\n  margin-bottom: 0.75rem;\n}\n\n.dark .markdown-content pre {\n  background-color: #374151;\n}\n\n.markdown-content code {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 0.85em;\n  border-radius: 0.25rem;\n  background-color: #f3f4f6;\n  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;\n}\n\n.dark .markdown-content code {\n  background-color: #374151;\n}\n\n.markdown-content pre code {\n  padding: 0;\n  background-color: transparent;\n}\n\n/* Blockquotes */\n.markdown-content blockquote {\n  padding: 0 1rem;\n  margin-left: 0;\n  margin-right: 0;\n  border-left: 0.25rem solid #e5e7eb;\n  color: #6b7280;\n}\n\n.dark .markdown-content blockquote {\n  border-left-color: #4b5563;\n  color: #9ca3af;\n}\n\n/* Tables */\n.markdown-content table {\n  display: block;\n  width: 100%;\n  overflow: auto;\n  border-spacing: 0;\n  border-collapse: collapse;\n  margin-bottom: 0.75rem;\n}\n\n.markdown-content table th {\n  font-weight: 600;\n  padding: 0.5rem 0.75rem;\n  border: 1px solid #e5e7eb;\n}\n\n.markdown-content table td {\n  padding: 0.5rem 0.75rem;\n  border: 1px solid #e5e7eb;\n}\n\n.dark .markdown-content table th,\n.dark .markdown-content table td {\n  border-color: #4b5563;\n}\n\n/* Links */\n.markdown-content a {\n  color: #3b82f6;\n  text-decoration: none;\n}\n\n.markdown-content a:hover {\n  text-decoration: underline;\n}\n\n.dark .markdown-content a {\n  color: #60a5fa;\n}\n\n/* Images */\n.markdown-content img {\n  max-width: 100%;\n  height: auto;\n  margin: 0.5rem 0;\n}\n\n/* Horizontal rule */\n.markdown-content hr {\n  height: 0.25rem;\n  padding: 0;\n  margin: 1.5rem 0;\n  background-color: #e5e7eb;\n  border: 0;\n}\n\n.dark .markdown-content hr {\n  background-color: #4b5563;\n}\n", ":root {\n  --sidebar-width: 280px;\n  --sidebar-expanded-width: 350px;\n  --transition-speed: 0.3s;\n  --border-color: #E5E7EB;\n  --hover-color: #F3F4F6;\n  --active-color: #E5E7EB;\n  --text-primary: #111827;\n  --text-secondary: #6B7280;\n  --bg-primary: #FFFFFF;\n  --bg-secondary: #F9FAFB;\n  --accent-color: #3B82F6;\n  --accent-hover: #2563EB;\n}\n\n.dark {\n  --border-color: #4B5563;\n  --hover-color: #374151;\n  --active-color: #4B5563;\n  --text-primary: #F9FAFB;\n  --text-secondary: #9CA3AF;\n  --bg-primary: #1F2937;\n  --bg-secondary: #111827;\n  --accent-color: #3B82F6;\n  --accent-hover: #2563EB;\n}\n\n.chat-history {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: var(--sidebar-width);\n  overflow: hidden;\n  transition: width var(--transition-speed) ease;\n  background-color: var(--bg-secondary);\n  border-right: 1px solid var(--border-color);\n}\n\n.chat-history.expanded {\n  width: var(--sidebar-expanded-width);\n}\n\n/* Admin specific chat history */\n.chat-history.in-admin-page {\n  height: calc(100vh - 65px); /* Adjusted height to fit in viewport */\n  max-height: calc(100vh - 65px); /* Adjusted height to fit in viewport */\n  overflow-y: auto;\n}\n\n.chat-history-header {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  padding: 1rem;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-primary);\n}\n\n.chat-history-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.chat-history-title h3 {\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0;\n  color: var(--text-primary);\n}\n\n.expand-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 0.25rem;\n  color: var(--text-secondary);\n}\n\n.expand-button:hover {\n  background-color: var(--hover-color);\n}\n\n.expand-icon {\n  height: 1rem;\n  width: 1rem;\n}\n\n.new-chat-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  padding: 0.5rem 0.75rem;\n  border-radius: 0.375rem;\n  background-color: var(--accent-color);\n  color: white;\n  border: none;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: background-color 0.2s;\n}\n\n.new-chat-button:hover {\n  background-color: var(--accent-hover);\n}\n\n.new-chat-icon {\n  height: 1rem;\n  width: 1rem;\n  margin-right: 0.25rem;\n}\n\n.chat-history-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0.5rem;\n  scrollbar-width: thin;\n  max-height: calc(100% - 80px); /* Fixed height for scrolling */\n}\n\n.chat-history-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-history-list::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-history-list::-webkit-scrollbar-thumb {\n  background-color: var(--text-secondary);\n  border-radius: 3px;\n  opacity: 0.7;\n}\n\n.chat-history-list::-webkit-scrollbar-thumb:hover {\n  background-color: var(--text-primary);\n  opacity: 1;\n}\n\n/* Admin specific chat history list */\n.admin-page .chat-history-list {\n  max-height: calc(100vh - 140px); /* Fixed height for admin */\n  height: auto;\n  overflow-y: auto;\n}\n\n.conversation-group {\n  margin-bottom: 1rem;\n}\n\n.date-header {\n  font-size: 0.75rem;\n  color: var(--text-secondary);\n  padding: 0.5rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.conversation-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.75rem;\n  border-radius: 0.375rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  margin-bottom: 0.25rem;\n}\n\n.conversation-item:hover {\n  background-color: var(--hover-color);\n}\n\n.conversation-item.active {\n  background-color: var(--active-color);\n}\n\n.conversation-content {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.conversation-icon {\n  height: 1.25rem;\n  width: 1.25rem;\n  margin-right: 0.5rem;\n  color: var(--text-secondary);\n  flex-shrink: 0;\n}\n\n.conversation-details {\n  overflow: hidden;\n  flex: 1;\n}\n\n.conversation-title {\n  font-size: 0.875rem;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  color: var(--text-primary);\n}\n\n.conversation-meta {\n  font-size: 0.75rem;\n  color: var(--text-secondary);\n  margin-top: 0.25rem;\n}\n\n.conversation-username {\n  display: flex;\n  align-items: center;\n  font-size: 0.75rem;\n  color: var(--text-secondary);\n  margin-bottom: 0.25rem;\n  font-style: italic;\n}\n\n.username-icon {\n  height: 0.875rem;\n  width: 0.875rem;\n  margin-right: 0.25rem;\n  color: var(--text-secondary);\n}\n\n.message-count {\n  font-size: 0.75rem;\n  color: var(--text-secondary);\n}\n\n.no-conversations {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  color: var(--text-secondary);\n  font-size: 0.875rem;\n  text-align: center;\n  padding: 1rem;\n}\n\n.load-more-container {\n  display: flex;\n  justify-content: center;\n  padding: 0.75rem 0;\n  position: sticky;\n  bottom: 0;\n  background-color: var(--bg-secondary);\n  border-top: 1px solid var(--border-color);\n  margin-top: 0.5rem;\n}\n\n.load-more-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 90%;\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  background-color: var(--hover-color);\n  color: var(--text-primary);\n  border: 1px solid var(--border-color);\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.load-more-button:hover {\n  background-color: var(--active-color);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.load-more-button:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.load-more-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.load-more-icon {\n  height: 0.875rem;\n  width: 0.875rem;\n  margin-left: 0.25rem;\n  transition: transform 0.2s ease;\n}\n\n.load-more-button:hover .load-more-icon {\n  transform: translateY(2px);\n}\n\n/* Delete button styles */\n.delete-button {\n  background: none;\n  border: none;\n  color: var(--text-secondary);\n  opacity: 0.5;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 0.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.delete-button:hover {\n  opacity: 1;\n  color: #ef4444;\n  background-color: rgba(239, 68, 68, 0.1);\n}\n\n.delete-icon {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Delete confirmation styles */\n.delete-confirm {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.delete-yes, .delete-no {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 0.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.delete-yes {\n  color: #ef4444;\n}\n\n.delete-yes:hover {\n  background-color: rgba(239, 68, 68, 0.2);\n}\n\n.delete-no {\n  color: var(--text-secondary);\n}\n\n.delete-no:hover {\n  background-color: var(--hover-color);\n}\n\n.cancel-icon {\n  width: 1rem;\n  height: 1rem;\n}\n", "/* Chat container styles */\n.chat-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n  overflow: hidden; /* Prevent scrolling on the container */\n}\n\n/* Chat main area */\n.chat-main {\n  flex: 1;\n  display: flex;\n  border-radius: 0.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  height: calc(100vh - 80px); /* Fixed height for the main chat area */\n}\n\n/* Admin specific container */\n.admin-page .chat-container {\n  overflow: visible; /* Allow scrolling on admin container */\n}\n\n.admin-page .chat-main {\n  overflow: visible; /* Allow scrolling on admin main area */\n  height: auto; /* Auto height for admin */\n  min-height: calc(100vh - 140px); /* Minimum height for admin */\n}\n\n/* Chat sidebar */\n.chat-sidebar {\n  height: 100%;\n  transition: all 0.3s ease;\n  width: 280px;\n  min-width: 280px;\n  max-width: 350px;\n  overflow: hidden;\n  border-right: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n}\n\n.chat-sidebar.expanded {\n  width: 350px;\n  min-width: 350px;\n}\n\n/* Admin specific sidebar */\n.admin-page .chat-sidebar {\n  height: 100vh; /* Full viewport height */\n  position: sticky;\n  top: 0;\n  overflow-y: auto; /* Allow scrolling in sidebar */\n  overflow-x: hidden;\n  max-height: calc(100vh - 20px); /* Prevent overflow */\n}\n\n/* Chat content area */\n.chat-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  max-width: 100%;\n  min-width: 0;\n  height: 100%; /* Ensure full height */\n  overflow: hidden; /* Prevent scrolling on the container */\n}\n\n/* Admin specific content area */\n.admin-page .chat-content {\n  height: auto; /* Auto height for admin */\n  min-height: calc(100vh - 140px); /* Minimum height for admin */\n  overflow: visible; /* Allow content to be visible */\n}\n\n/* Chat header */\n.chat-header {\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid var(--border-color);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n/* Chat messages area */\n.chat-messages {\n  flex: 1;\n  padding: 0.75rem 1rem;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  max-height: calc(100vh - 160px); /* Fixed height for scrolling */\n  height: 100%;\n}\n\n.chat-messages::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n  background-color: var(--text-secondary);\n  border-radius: 3px;\n  opacity: 0.7;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n  background-color: var(--text-primary);\n  opacity: 1;\n}\n\n/* Admin specific messages area */\n.admin-page .chat-messages {\n  max-height: none; /* Remove max height for admin */\n  height: auto; /* Auto height for admin */\n  overflow: visible; /* Allow content to be visible */\n}\n\n/* Message groups */\n.message-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  min-height: 100%;\n  padding-bottom: 0.5rem; /* Add padding at the bottom for better spacing */\n}\n\n/* Empty state */\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  padding: 2rem;\n  color: var(--text-secondary);\n  text-align: center;\n}\n\n/* Message input area */\n.chat-input-container {\n  padding: 0.75rem 1rem;\n  border-top: 1px solid var(--border-color);\n}\n\n.chat-input-form {\n  display: flex;\n  align-items: center;\n}\n\n.chat-input {\n  flex: 1;\n  padding: 0.75rem 1rem;\n  border-radius: 0.375rem;\n  border: none;\n  outline: none;\n  font-size: 0.875rem;\n}\n\n.chat-send-button {\n  margin-left: 0.5rem;\n  padding: 0.75rem;\n  border-radius: 0.375rem;\n  border: none;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Dark mode specific styles */\n.dark .chat-input {\n  background-color: var(--bg-secondary);\n  color: var(--text-primary);\n}\n\n.dark .chat-send-button {\n  background-color: var(--accent-color);\n  color: white;\n}\n\n.dark .chat-send-button:disabled {\n  background-color: var(--text-secondary);\n  cursor: not-allowed;\n}\n\n/* Responsive styles */\n@media (max-width: 768px) {\n  .chat-sidebar {\n    position: absolute;\n    z-index: 10;\n    height: 100%;\n    background-color: var(--bg-primary);\n    width: 280px;\n  }\n}\n\n@media (min-width: 1024px) {\n  .chat-main {\n    height: calc(100vh - 80px);\n  }\n\n  .chat-messages {\n    max-height: calc(100vh - 160px);\n  }\n}\n\n/* Admin specific styles */\n.admin-page .chat-main {\n  height: calc(100vh - 120px);\n}\n\n.admin-page .chat-messages {\n  max-height: calc(100vh - 200px);\n}\n", "/* Modern Chat Interface Styles */\n:root {\n  --chat-radius: 1rem;\n  --chat-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --chat-transition: all 0.3s ease;\n  --user-message-bg: #3B82F6;\n  --user-message-color: #FFFFFF;\n  --bot-message-bg: #FFFFFF;\n  --bot-message-color: #111827;\n  --system-message-bg: #F3F4F6;\n  --system-message-color: #4B5563;\n  --chat-input-bg: #F3F4F6;\n  --chat-input-color: #111827;\n  --chat-input-placeholder: #9CA3AF;\n  --chat-send-button-bg: #3B82F6;\n  --chat-send-button-color: #FFFFFF;\n  --chat-send-button-disabled-bg: #9CA3AF;\n  --chat-header-border: #E5E7EB;\n  --chat-timestamp-color: #9CA3AF;\n  --chat-typing-dot-color: #9CA3AF;\n  --chat-references-bg: #F3F4F6;\n  --chat-references-color: #4B5563;\n  --chat-references-border: #E5E7EB;\n  --chat-references-link: #2563EB;\n}\n\n/* Dark mode variables */\n.dark {\n  --user-message-bg: #2563EB;\n  --user-message-color: #FFFFFF;\n  --bot-message-bg: #374151;\n  --bot-message-color: #F9FAFB;\n  --system-message-bg: #4B5563;\n  --system-message-color: #F9FAFB;\n  --chat-input-bg: #374151;\n  --chat-input-color: #F9FAFB;\n  --chat-input-placeholder: #9CA3AF;\n  --chat-send-button-bg: #2563EB;\n  --chat-send-button-color: #FFFFFF;\n  --chat-send-button-disabled-bg: #4B5563;\n  --chat-header-border: #4B5563;\n  --chat-timestamp-color: #9CA3AF;\n  --chat-typing-dot-color: #9CA3AF;\n  --chat-references-bg: #4B5563;\n  --chat-references-color: #F9FAFB;\n  --chat-references-border: #6B7280;\n  --chat-references-link: #60A5FA;\n}\n\n/* Chat container */\n.modern-chat-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n  background-color: var(--bg-primary);\n  transition: var(--chat-transition);\n}\n\n/* Chat main area */\n.modern-chat-main {\n  flex: 1;\n  display: flex;\n  border-radius: var(--chat-radius);\n  box-shadow: var(--chat-shadow);\n  overflow: hidden;\n  height: 100%;\n  min-height: 500px; /* Reduced minimum height */\n  background-color: var(--bg-secondary);\n  transition: var(--chat-transition);\n  position: relative; /* Add position relative for z-index context */\n  z-index: 1; /* Base z-index */\n}\n\n/* Chat content area */\n.modern-chat-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  max-width: 100%;\n  min-width: 0;\n  height: 100%;\n  overflow: hidden;\n  background-color: var(--bg-primary);\n  transition: var(--chat-transition);\n  position: relative;\n  z-index: 1;\n}\n\n/* Chat header */\n.modern-chat-header {\n  padding: 0.5rem 1rem;\n  border-bottom: 1px solid var(--chat-header-border);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background-color: var(--bg-primary);\n  transition: var(--chat-transition);\n  flex: 0 0 auto; /* Don't allow flex to grow or shrink */\n  z-index: 3;\n  height: auto; /* Auto height */\n  min-height: 60px; /* Minimum height */\n}\n\n.modern-chat-header-left {\n  display: flex;\n  align-items: center;\n}\n\n.modern-sidebar-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2.5rem;\n  height: 2.5rem;\n  border-radius: 0.5rem;\n  background-color: var(--bg-secondary);\n  color: var(--text-secondary);\n  border: none;\n  cursor: pointer;\n  margin-right: 0.75rem;\n  transition: var(--chat-transition);\n}\n\n.modern-sidebar-toggle:hover {\n  background-color: var(--hover-color);\n  transform: scale(1.05);\n}\n\n.modern-ai-avatar {\n  height: 2rem;\n  width: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--accent-color);\n  background: linear-gradient(135deg, #3B82F6, #2563EB);\n  margin-right: 0.5rem;\n  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);\n}\n\n.modern-ai-avatar span {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: white;\n}\n\n.modern-ai-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.modern-ai-name {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 0.15rem;\n  white-space: nowrap;\n}\n\n.modern-ai-status {\n  display: flex;\n  align-items: center;\n}\n\n.modern-status-indicator {\n  height: 0.4rem;\n  width: 0.4rem;\n  border-radius: 50%;\n  margin-right: 0.4rem;\n  transition: var(--chat-transition);\n}\n\n.modern-status-indicator.ready {\n  background-color: #10B981;\n  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);\n}\n\n.modern-status-indicator.initializing {\n  background-color: #F59E0B;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);\n  animation: pulse 1.5s infinite;\n}\n\n.modern-status-text {\n  font-size: 0.7rem;\n  color: var(--text-secondary);\n}\n\n/* Chat action buttons */\n.modern-chat-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.modern-action-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  border-radius: 0.5rem;\n  background-color: var(--bg-secondary);\n  color: var(--text-secondary);\n  border: none;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: var(--chat-transition);\n  white-space: nowrap;\n}\n\n.modern-action-button:hover {\n  background-color: var(--hover-color);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.modern-action-button:active {\n  transform: translateY(0);\n}\n\n.modern-action-button svg {\n  height: 1rem;\n  width: 1rem;\n}\n\n/* Chat messages area */\n.modern-chat-messages {\n  flex: 1 1 auto;\n  padding: 0.75rem 1rem;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  background-color: var(--bg-primary);\n  background-image:\n    radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.05) 2px, transparent 0),\n    radial-gradient(circle at 75px 75px, rgba(59, 130, 246, 0.05) 2px, transparent 0);\n  background-size: 100px 100px;\n  transition: var(--chat-transition);\n  position: relative;\n  z-index: 2;\n  min-height: 0; /* Allow container to shrink below content size */\n}\n\n.modern-chat-messages::-webkit-scrollbar {\n  width: 6px;\n}\n\n.modern-chat-messages::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.modern-chat-messages::-webkit-scrollbar-thumb {\n  background-color: var(--text-secondary);\n  border-radius: 3px;\n  opacity: 0.7;\n}\n\n.modern-chat-messages::-webkit-scrollbar-thumb:hover {\n  background-color: var(--text-primary);\n  opacity: 1;\n}\n\n.modern-message-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  min-height: 100%;\n  padding-bottom: 0.5rem;\n  position: relative;\n  z-index: 2;\n}\n\n/* Message styles */\n.modern-message-container {\n  display: flex;\n  max-width: 100%;\n  animation: fadeIn 0.3s ease;\n}\n\n.modern-message-container.user {\n  justify-content: flex-end;\n}\n\n.modern-message-container.bot {\n  justify-content: flex-start;\n}\n\n.modern-message-container.system {\n  justify-content: center;\n}\n\n.modern-message-bubble {\n  max-width: 80%;\n  border-radius: 0.75rem;\n  padding: 0.75rem;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  overflow-wrap: break-word;\n  word-wrap: break-word;\n  word-break: break-word;\n  transition: var(--chat-transition);\n  position: relative;\n}\n\n.modern-message-bubble.user {\n  background-color: var(--user-message-bg);\n  color: var(--user-message-color);\n  border-bottom-right-radius: 0.25rem;\n}\n\n.modern-message-bubble.bot {\n  background-color: var(--bot-message-bg);\n  color: var(--bot-message-color);\n  border-bottom-left-radius: 0.25rem;\n}\n\n.modern-message-bubble.system {\n  background-color: var(--system-message-bg);\n  color: var(--system-message-color);\n  max-width: 60%;\n  text-align: center;\n  font-style: italic;\n}\n\n.modern-message-content {\n  font-size: 0.9375rem;\n  line-height: 1.5;\n  white-space: pre-wrap;\n}\n\n.modern-message-timestamp {\n  font-size: 0.75rem;\n  margin-top: 0.5rem;\n  color: var(--chat-timestamp-color);\n  text-align: right;\n  opacity: 0.8;\n}\n\n/* Document references */\n.modern-document-references {\n  margin-top: 1rem;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  background-color: var(--chat-references-bg);\n  border: 1px solid var(--chat-references-border);\n}\n\n.modern-references-title {\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: var(--chat-references-color);\n}\n\n.modern-references-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.modern-reference-item {\n  font-size: 0.8125rem;\n  color: var(--chat-references-color);\n}\n\n.modern-reference-link {\n  color: var(--chat-references-link);\n  text-decoration: none;\n  font-weight: 500;\n}\n\n.modern-reference-link:hover {\n  text-decoration: underline;\n}\n\n/* Typing indicator */\n.modern-typing-indicator {\n  display: flex;\n  justify-content: flex-start;\n  animation: fadeIn 0.3s ease;\n}\n\n.modern-typing-bubble {\n  border-radius: 1rem;\n  padding: 0.75rem 1rem;\n  background-color: var(--bot-message-bg);\n  color: var(--bot-message-color);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  border-bottom-left-radius: 0.25rem;\n}\n\n.modern-typing-dots {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.modern-typing-dot {\n  width: 0.5rem;\n  height: 0.5rem;\n  border-radius: 50%;\n  background-color: var(--chat-typing-dot-color);\n}\n\n.modern-typing-dot:nth-child(1) {\n  animation: bounce 1.4s infinite 0.2s;\n}\n\n.modern-typing-dot:nth-child(2) {\n  animation: bounce 1.4s infinite 0.4s;\n}\n\n.modern-typing-dot:nth-child(3) {\n  animation: bounce 1.4s infinite 0.6s;\n}\n\n/* Error message */\n.modern-error-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 1rem;\n  animation: fadeIn 0.3s ease;\n}\n\n.modern-error-message {\n  padding: 0.75rem 1rem;\n  border-radius: 0.5rem;\n  background-color: rgba(220, 38, 38, 0.1);\n  color: #DC2626;\n  font-size: 0.875rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.modern-error-icon {\n  height: 1rem;\n  width: 1rem;\n}\n\n/* Chat input area */\n.modern-chat-input-container {\n  padding: 0.5rem 1rem; /* Reduced padding */\n  border-top: 1px solid var(--chat-header-border);\n  background-color: var(--bg-primary);\n  transition: var(--chat-transition);\n  position: relative;\n  z-index: 3;\n  flex: 0 0 auto; /* Don't allow flex to grow or shrink */\n}\n\n.modern-chat-input-form {\n  display: flex;\n  align-items: flex-end;\n  position: relative;\n}\n\n.modern-chat-input {\n  flex: 1;\n  padding: 0.75rem 1rem;\n  padding-right: 3rem;\n  border-radius: 1.5rem;\n  border: 1px solid var(--chat-header-border);\n  outline: none;\n  font-size: 0.9375rem;\n  line-height: 1.5;\n  resize: none;\n  max-height: 150px;\n  min-height: 2.5rem;\n  background-color: var(--chat-input-bg);\n  color: var(--chat-input-color);\n  transition: var(--chat-transition);\n}\n\n.modern-chat-input:focus {\n  border-color: var(--accent-color);\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);\n}\n\n.modern-chat-input::placeholder {\n  color: var(--chat-input-placeholder);\n  text-align: center;\n}\n\n.modern-chat-send-button {\n  position: absolute;\n  right: 0.75rem;\n  bottom: 0.5rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  background-color: var(--chat-send-button-bg);\n  color: var(--chat-send-button-color);\n  border: none;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: var(--chat-transition);\n}\n\n.modern-chat-send-button:hover:not(:disabled) {\n  transform: scale(1.05);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.modern-chat-send-button:disabled {\n  background-color: var(--chat-send-button-disabled-bg);\n  cursor: not-allowed;\n}\n\n.modern-send-icon {\n  height: 1.25rem;\n  width: 1.25rem;\n  transform: rotate(45deg); /* Adjust rotation to point up-right */\n}\n\n/* Animations */\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes bounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-5px); }\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n/* History Dropdown Styles */\n.history-dropdown-container {\n  position: relative;\n}\n\n.history-dropdown-content {\n  position: absolute;\n  top: calc(100% + 0.5rem);\n  right: 0;\n  width: 300px;\n  max-height: 400px;\n  overflow-y: auto;\n  background-color: var(--bg-primary);\n  border-radius: 0.5rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  z-index: 100;\n  border: 1px solid var(--chat-header-border);\n  animation: fadeIn 0.2s ease;\n  /* Ensure dropdown doesn't affect layout */\n  position: absolute;\n}\n\n.history-dropdown-content .chat-history {\n  width: 100%;\n  height: auto;\n  max-height: 400px;\n  border-right: none;\n}\n\n.history-dropdown-content .chat-history-list {\n  max-height: 300px;\n}\n\n/* Admin specific styles */\n.admin-page .modern-chat-container {\n  height: calc(100vh - 65px); /* Adjusted height to fit in viewport */\n  position: relative;\n  z-index: 1;\n  max-width: 100%;\n  overflow: hidden;\n}\n\n.admin-page .modern-chat-main {\n  height: 100%;\n}\n\n/* Fixed height for chat messages area with scrollbar */\n.admin-page .modern-chat-messages {\n  flex: 1 1 auto; /* Allow flex to grow and shrink */\n  overflow-y: auto;\n  min-height: 200px; /* Minimum height */\n  max-height: calc(100vh - 180px); /* Maximum height */\n}\n\n/* Responsive styles */\n@media (max-width: 1200px) {\n  .modern-action-button {\n    padding: 0.4rem 0.6rem;\n    font-size: 0.8rem;\n  }\n\n  .modern-action-button svg {\n    height: 0.9rem;\n    width: 0.9rem;\n  }\n}\n\n@media (max-width: 992px) {\n  .modern-chat-actions {\n    gap: 0.3rem;\n  }\n\n  .modern-action-button {\n    padding: 0.3rem 0.5rem;\n    font-size: 0.75rem;\n  }\n\n  .modern-action-button svg {\n    height: 0.85rem;\n    width: 0.85rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .modern-chat-messages {\n    padding: 0.75rem;\n  }\n\n  .modern-message-bubble {\n    max-width: 85%;\n  }\n\n  .modern-chat-header {\n    padding: 0.4rem 0.75rem;\n  }\n\n  .modern-ai-name {\n    font-size: 0.85rem;\n  }\n\n  .modern-action-button span {\n    display: none;\n  }\n\n  .modern-action-button {\n    padding: 0.4rem;\n  }\n\n  .modern-chat-input-container {\n    padding: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .modern-chat-actions {\n    gap: 0.2rem;\n  }\n\n  .modern-action-button {\n    padding: 0.3rem;\n  }\n\n  .modern-ai-avatar {\n    height: 1.75rem;\n    width: 1.75rem;\n  }\n}\n\n@media (max-width: 640px) {\n  .modern-chat-actions {\n    gap: 0.5rem;\n  }\n\n  .modern-action-button {\n    padding: 0.5rem;\n  }\n\n  .modern-action-button span {\n    display: none;\n  }\n\n  .history-dropdown-content {\n    width: 250px;\n    right: -50px;\n  }\n}\n\n@media (max-width: 480px) {\n  .modern-message-bubble {\n    max-width: 90%;\n  }\n\n  .modern-ai-name {\n    font-size: 0.875rem;\n  }\n\n  .modern-chat-header {\n    padding: 0.5rem 0.75rem;\n  }\n\n  .modern-chat-messages {\n    padding: 0.5rem 0.75rem;\n  }\n\n  .modern-chat-input-container {\n    padding: 0.5rem 0.75rem;\n  }\n\n  .modern-chat-input {\n    padding: 0.5rem 0.75rem;\n    padding-right: 2.5rem;\n    font-size: 0.875rem;\n  }\n\n  .modern-chat-send-button {\n    width: 1.75rem;\n    height: 1.75rem;\n  }\n\n  .modern-send-icon {\n    height: 1rem;\n    width: 1rem;\n  }\n}\n"]}