(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{323:function(e,t,r){},324:function(e,t,r){},379:function(e,t,r){e.exports=r(614)},387:function(e,t,r){},388:function(e,t,r){},389:function(e,t,r){},390:function(e,t,r){},391:function(e,t,r){},392:function(e,t,r){},393:function(e,t,r){},394:function(e,t,r){},612:function(e,t,r){},613:function(e,t,r){},614:function(e,t,r){"use strict";r.r(t);var a=r(0),o=r.n(a),n=r(325),l=r.n(n),i=(r(387),r(388),r(11)),s=r(57),c=(r(389),r(390),r(391),r(392),r(393),r(665)),d=r(666),m=r(667),u=r(668),g=r(669);const p=Object(a.createContext)(),h=()=>Object(a.useContext)(p);var f=e=>{let{children:t}=e;const[r,n]=Object(a.useState)(()=>{const e=localStorage.getItem("darkMode");return null!==e?"true"===e:!(!window.matchMedia||!window.matchMedia("(prefers-color-scheme: dark)").matches)});Object(a.useEffect)(()=>{console.log("ThemeContext - Applying theme:",r?"dark":"light"),r?(document.documentElement.classList.add("dark"),document.body.classList.add("dark"),document.body.classList.add("force-dark-mode"),document.body.style.backgroundColor="#1F2937",document.body.style.color="#F9FAFB"):(document.documentElement.classList.remove("dark"),document.body.classList.remove("dark"),document.body.classList.remove("force-dark-mode"),document.body.style.backgroundColor="#FFFFFF",document.body.style.color="#111827"),document.body.style.display="none";const e=document.body.offsetHeight;document.body.style.display="",console.log("Forced reflow:",e),localStorage.setItem("darkMode",r.toString()),console.log("ThemeContext - Theme applied, body classes:",document.body.classList.toString())},[r]);Object(a.useEffect)(()=>{const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{null===localStorage.getItem("darkMode")&&n(e.matches)};return e.addEventListener?e.addEventListener("change",t):e.addListener(t),()=>{e.removeEventListener?e.removeEventListener("change",t):e.removeListener(t)}},[]);const l={colors:r?{background:{primary:"#1F2937",secondary:"#374151",tertiary:"#4B5563"},text:{primary:"#F9FAFB",secondary:"#D1D5DB",tertiary:"#9CA3AF"},border:{primary:"#4B5563",secondary:"#6B7280"},accent:{primary:"#3B82F6",secondary:"#60A5FA"},success:{primary:"#059669",light:"#A7F3D0",background:"#05966930"},error:{primary:"#DC2626",light:"#FCA5A5",background:"#DC262630"},warning:{primary:"#D97706",light:"#FCD34D",background:"#********"},info:{primary:"#2563EB",light:"#BFDBFE",background:"#2563EB30"},admin:{primary:"#7E22CE",light:"#E9D5FF",background:"#7E22CE30"},user:{primary:"#1D4ED8",light:"#BFDBFE",background:"#1D4ED830"}}:{background:{primary:"#FFFFFF",secondary:"#F9FAFB",tertiary:"#F3F4F6"},text:{primary:"#111827",secondary:"#6B7280",tertiary:"#9CA3AF"},border:{primary:"#E5E7EB",secondary:"#D1D5DB"},accent:{primary:"#2563EB",secondary:"#3B82F6"},success:{primary:"#059669",light:"#D1FAE5",background:"#D1FAE5"},error:{primary:"#DC2626",light:"#FEE2E2",background:"#FEE2E2"},warning:{primary:"#D97706",light:"#FEF3C7",background:"#FEF3C7"},info:{primary:"#2563EB",light:"#DBEAFE",background:"#DBEAFE"},admin:{primary:"#7E22CE",light:"#F3E8FF",background:"#F3E8FF"},user:{primary:"#1D4ED8",light:"#DBEAFE",background:"#DBEAFE"}},darkMode:r,toggleDarkMode:()=>{console.log("ThemeContext - toggleDarkMode called, current value:",r),n(e=>(console.log("ThemeContext - Setting darkMode from",e,"to",!e),!e))}};return o.a.createElement(p.Provider,{value:l},t)};var y=e=>{let{sidebarOpen:t,setSidebarOpen:r}=e;const a=h();console.log("Sidebar - Theme state:",{darkMode:a.darkMode,themeColors:a.colors});const n=[{name:"Dashboard",icon:c.a,path:"/admin/dashboard"},{name:"Chat",icon:d.a,path:"/admin/chat"},{name:"User & Group Management",icon:m.a,path:"/admin/users"},{name:"Admin Settings",icon:u.a,path:"/admin/settings"}];return o.a.createElement(o.a.Fragment,null,t,o.a.createElement("div",{className:`sidebar ${t?"open":""}`},o.a.createElement("div",{className:"sidebar-header"},o.a.createElement("div",{style:{display:"flex",alignItems:"center"}},o.a.createElement("img",{src:"/logo.svg",alt:"Logo",style:{height:"2rem",width:"2rem",marginRight:"0.5rem"}}),o.a.createElement("span",{style:{fontSize:"1.125rem",fontWeight:600}},"Admin Panel")),o.a.createElement("button",{style:{background:"none",border:"none",cursor:"pointer",color:a.colors.text.tertiary},onClick:()=>r(!1)},o.a.createElement(g.a,{style:{height:"1.5rem",width:"1.5rem"}}))),o.a.createElement("nav",{className:"sidebar-nav"},o.a.createElement("ul",null,n.map(e=>o.a.createElement("li",{key:e.name},o.a.createElement(s.b,{to:e.path,className:e=>{let{isActive:t}=e;return t?"active":""}},o.a.createElement(e.icon,{style:{height:"1.25rem",width:"1.25rem",marginRight:"0.75rem"}}),o.a.createElement("span",null,e.name)))))),o.a.createElement("div",{style:{position:"absolute",bottom:0,width:"100%",padding:"1rem",borderTop:`1px solid ${a.colors.border.primary}`,fontSize:"0.75rem",color:a.colors.text.tertiary}},o.a.createElement("p",null,"\xa9 2025 Admin Panel"),o.a.createElement("p",null,"Version 1.0.0"))))},E=r(673),b=r(674),v=r(675),w=r(676),x=r(677);let F="http://localhost:8000";try{window._env_&&window._env_.REACT_APP_API_URL?F=window._env_.REACT_APP_API_URL:window.location.origin&&(F=window.location.origin,console.log("Using API URL from current origin:",F),"localhost"!==window.location.hostname&&"127.0.0.1"!==window.location.hostname||"8000"!==window.location.port&&(console.log("Development environment detected, using port 8000 for API"),F=`${window.location.protocol}//${window.location.hostname}:8000`))}catch(Xr){console.log("Error setting API URL, using default:",F,Xr)}console.log("Final API URL:",F);(async()=>{const e=[F,"http://localhost:8000",window.location.origin,`${window.location.protocol}//${window.location.hostname}:8000`],t=[...new Set(e)];console.log("Checking API connectivity with these URLs:",t);for(const a of t)try{console.log("Trying API URL:",a);const e=await fetch(`${a}/docs`);if(console.log(`API connectivity check for ${a} status:`,e.status,e.statusText),e.ok||401===e.status||403===e.status)return console.log(`API connectivity check successful for ${a}`),a!==F&&(console.log(`Updating API_BASE_URL from ${F} to ${a}`),F=a),!0}catch(r){console.warn(`API connectivity check failed for ${a} with error:`,r)}console.error("All API connectivity checks failed. Using default URL:",F)})();const D=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r={...t,credentials:"include",headers:{"Content-Type":"application/json",...t.headers}},a=localStorage.getItem("authToken");a&&!r.headers.Authorization&&(r.headers.Authorization=`Bearer ${a}`);try{return await fetch(e,r)}catch(o){throw console.error(`Fetch error for ${e}:`,o),o}},C=async e=>{if(console.log(`API Response: ${e.url} - Status: ${e.status} ${e.statusText}`),401===e.status)return localStorage.removeItem("authToken"),localStorage.removeItem("userData"),window.location.pathname.includes("/login")?console.log("Unauthorized response, but already on login page"):(console.log("Unauthorized response, redirecting to login"),window.location.href="/login"),Promise.reject({message:"Authentication failed. Please log in again."});const t=e.headers.get("content-type");if(t&&t.includes("text/html")){console.log("Received HTML response instead of JSON. API endpoint may be incorrect or server error occurred."),console.log("Response status:",e.status,e.statusText);const t=await e.text();return console.log("HTML response preview:",t.substring(0,200)+"..."),Promise.reject({message:"Server returned HTML instead of JSON. API endpoint may be incorrect.",status:e.status})}const r=e.clone();let a;try{a=await e.json(),console.log("API Response data:",a)}catch(o){console.error("Error parsing JSON response:",o);try{const e=await r.text();console.error("Raw response:",e.substring(0,200)+"...")}catch(Xr){console.error("Could not get raw response:",Xr)}return Promise.reject({message:"Invalid response format from server",originalError:o.message})}if(!e.ok){let t;return console.error("API Error Response:",{status:e.status,statusText:e.statusText,data:a}),(t=a&&a.detail?"object"===typeof a.detail?JSON.stringify(a.detail):a.detail:e.statusText).includes("Google API key or service account file must be provided")&&console.error("Google API configuration error detected"),(t.includes("not bound to a Session")||t.includes("lazy load operation"))&&(console.error("SQLAlchemy session error detected"),t="Database session error. Please try again."),Promise.reject({message:t,status:e.status,data:a})}return a},S=()=>localStorage.getItem("authToken"),B=async(e,t)=>{try{console.log("Attempting login to:",`${F}/token`);const o=new URLSearchParams;o.append("username",e),o.append("password",t);try{const e=await D(`${F}/token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:o});console.log("Login response status:",e.status,e.statusText);const t=e.headers.get("content-type");if(!t||!t.includes("text/html"))return C(e);{console.log("Received HTML response instead of JSON during login. API endpoint may be incorrect.");const t=await e.text();console.log("HTML login response preview:",t.substring(0,200)+"...")}}catch(r){console.log("Error with credentials-included fetch, trying fallback:",r)}console.log("Trying fallback login approach without credentials");const n=await fetch(`${F}/token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:o});return console.log("Fallback login response status:",n.status,n.statusText),C(n)}catch(a){throw console.error("Login error:",a),a}},k=async(e,t)=>{try{try{const a=await D(`${F}/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t,role:"user"})}),o=a.headers.get("content-type");if(!o||!o.includes("text/html"))return C(a);console.log("Received HTML response instead of JSON during registration. API endpoint may be incorrect.")}catch(r){console.log("Error with credentials-included fetch for registration, trying fallback:",r)}console.log("Trying fallback registration approach without credentials");const o=await fetch(`${F}/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t,role:"user"})});return C(o)}catch(a){throw console.error("Registration error:",a),a}},A=async()=>{try{const r=await D(`${F}/users`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(r)}catch(e){console.error("Error fetching users with fetchWithAuth, trying fallback:",e);try{const e=await fetch(`${F}/users`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)}catch(t){return console.error("Fallback fetch also failed:",t),{users:[]}}}},j=async e=>{try{var t;if(e===(null===(t=JSON.parse(localStorage.getItem("userData")))||void 0===t?void 0:t.id)){const e=await D(`${F}/users/me/details`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)}const a=await D(`${F}/users/${e}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(a)}catch(r){return console.error("Error fetching user details:",r),{error:"Failed to fetch user details"}}},O=async e=>{const t=await D(`${F}/users`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(e)});return C(t)},N=async(e,t)=>{const r=await D(`${F}/users/${e}`,{method:"PUT",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(t)});return C(r)},z=async e=>{const t=await D(`${F}/users/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)},T=async()=>{try{const a=localStorage.getItem("userData"),o=a?JSON.parse(a):null;if(o&&"admin"===o.role){const e=await fetch(`${F}/groups`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)}{const r=await fetch(`${F}/users/me/details`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}}),a=await C(r);if(a&&a.user&&a.user.groups){const t=a.user.groups;let r=[];if(a.user.group_details&&Array.isArray(a.user.group_details))r=a.user.group_details.map(e=>({id:e.id,name:e.name||`Group ${e.id.substring(0,8)}`,description:e.description||""}));else{r=t.map(e=>({id:e,name:`Group ${e.substring(0,8)}`}));try{localStorage.setItem("userGroups",JSON.stringify(r))}catch(e){console.error("Error storing groups in localStorage:",e)}}return console.log("User groups from /users/me/details:",r),r}try{const e=localStorage.getItem("userGroups");if(e)return JSON.parse(e)}catch(t){console.error("Error reading cached groups:",t)}return[]}}catch(r){console.error("Error fetching groups:",r);try{const e=localStorage.getItem("userGroups");if(e)return JSON.parse(e)}catch(t){console.error("Error reading cached groups:",t)}return[]}},$=async e=>{const t=await fetch(`${F}/groups`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(e)});return C(t)},I=async(e,t)=>{const r=await fetch(`${F}/groups/${e}`,{method:"PUT",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(t)});return C(r)},R=async e=>{const t=await fetch(`${F}/groups/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)},W=async(e,t)=>{const r=await fetch(`${F}/groups/${e}/users/${t}`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(r)},_=async(e,t)=>{const r=await fetch(`${F}/groups/${e}/users/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(r)},P=async()=>{try{const t=await D(`${F}/document-metadata/service-names`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}catch(e){console.error("Error fetching service names:",e);const t=await fetch(`${F}/document-metadata/service-names`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}},L=async e=>{const t=new FormData;t.append("name",e);try{const e=await D(`${F}/document-metadata/service-names`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}catch(r){console.error("Error creating service name:",r);const e=await fetch(`${F}/document-metadata/service-names`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}},U=async()=>{try{const t=await D(`${F}/document-metadata/software-menus`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}catch(e){console.error("Error fetching software menus:",e);const t=await fetch(`${F}/document-metadata/software-menus`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}},M=async e=>{const t=new FormData;t.append("name",e);try{const e=await D(`${F}/document-metadata/software-menus`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}catch(r){console.error("Error creating software menu:",r);const e=await fetch(`${F}/document-metadata/software-menus`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}},G=async()=>{try{const t=await D(`${F}/document-metadata/issue-types`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}catch(e){console.error("Error fetching issue types:",e);const t=await fetch(`${F}/document-metadata/issue-types`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}},H=async e=>{const t=new FormData;t.append("name",e);try{const e=await D(`${F}/document-metadata/issue-types`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}catch(r){console.error("Error creating issue type:",r);const e=await fetch(`${F}/document-metadata/issue-types`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:t});return C(e)}},J=async()=>{try{const t=await D(`${F}/document-status`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}catch(e){console.error("Error fetching documents with fetchWithAuth, trying fallback:",e);const t=await fetch(`${F}/document-status`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)}},X=async e=>{try{const r=await D(`${F}/upload`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:e});return C(r)}catch(t){console.error("Error uploading document with fetchWithAuth, trying fallback:",t);const r=await fetch(`${F}/upload`,{method:"POST",headers:{Authorization:`Bearer ${S()}`},body:e});return C(r)}},Y=async e=>{try{console.log(`Attempting to delete document with ID: ${e}`);const a=await D(`${F}/documents/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}}),o=await C(a);return console.log("Document deletion successful:",o),o}catch(t){if(404===t.status)return console.log(`Document with ID ${e} not found (may have been already deleted)`),{success:!0,message:"Document already deleted or not found",status:404};console.error("Error deleting document with fetchWithAuth, trying fallback:",t);try{const t=await fetch(`${F}/documents/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}}),a=await C(t);return console.log("Document deletion successful (fallback):",a),a}catch(r){if(404===r.status)return console.log(`Document with ID ${e} not found in fallback (may have been already deleted)`),{success:!0,message:"Document already deleted or not found",status:404};throw console.error("Fallback document deletion also failed:",r),r}}},V=async(e,t)=>{try{const a=await D(`${F}/document-access`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify({doc_id:e,groups:t})});return C(a)}catch(r){console.error("Error setting document access with fetchWithAuth, trying fallback:",r);const a=await fetch(`${F}/document-access`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify({doc_id:e,groups:t})});return C(a)}},q=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3600;try{const a=await D(`${F}/document-url/${e}?expiration=${t}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(a)}catch(r){console.error("Error getting document URL with fetchWithAuth, trying fallback:",r);const a=await fetch(`${F}/document-url/${e}?expiration=${t}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(a)}},K=async()=>{const e=await fetch(`${F}/config/google-project-id`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},Q=async e=>{const t=await fetch(`${F}/init`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(e)});return C(t)},Z=async e=>{const t=await fetch(`${F}/chat`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify({message:e})});return C(t)},ee=async()=>{const e=await fetch(`${F}/conversations/recent`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},te=async e=>{const t=await fetch(`${F}/conversations/${e}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(t)},re=async()=>{console.log("Refreshing session...");try{const t=await D(`${F}/users/me`,{method:"GET",credentials:"include",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return console.log("Refresh response status:",t.status),C(t)}catch(e){return console.error("Network error during session refresh:",e),{success:!1,error:"Network error during session refresh"}}},ae=async()=>{try{return console.log("Logging out client-side only (no server endpoint yet)"),{success:!0}}catch(e){return console.error("Error during logout:",e),{success:!0}}},oe=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{path:r,domain:a}=t;let o=`${e}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;r&&(o+=`; path=${r}`),a&&(o+=`; domain=${a}`),document.cookie=o},ne=Object(a.createContext)(),le=e=>{let{children:t}=e;const[r,n]=Object(a.useState)(null),[l,i]=Object(a.useState)(!1),[s,c]=Object(a.useState)(!0),d=()=>{try{const t=(e=>{const t=document.cookie.split(";");for(let r=0;r<t.length;r++){const a=t[r].trim();if(a.startsWith(e+"="))return a.substring(e.length+1)}return null})("user_data");if(t)try{const e=JSON.parse(t);if(e&&e.id&&e.username)return console.log("Found user data in cookie:",e),n(e),i(!0),!0}catch(Xr){console.log("Error parsing user data cookie, but continuing:",Xr)}const r=document.cookie;return!(!r.includes("access_token")&&!r.includes("refresh_token"))&&(console.log("Found authentication cookies, but no user data. Will try to refresh session."),!1)}catch(e){return console.log("Error checking user auth from cookies, but continuing:",e),!1}},m=async e=>{if(!e||!e.id)return e;try{const r=await(async e=>{try{try{const e=await fetch("http://localhost:8000/users/me/details",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("authToken")}`,"Content-Type":"application/json"}});if(e.ok){const t=await e.json();if(t&&t.user&&t.user.groups)return console.log("Fetched user details with groups from /users/me/details:",t.user),t.user.groups||[]}}catch(t){console.error("Error fetching from /users/me/details, trying fallback:",t)}const o=await j(e);return o&&o.user?(console.log("Fetched user details with groups from getUserById:",o.user),o.user.groups||[]):[]}catch(r){console.error("Error fetching user groups:",r);try{const e=localStorage.getItem("userData");if(e){const t=JSON.parse(e);if(t.groups&&Array.isArray(t.groups))return console.log("Using groups from localStorage:",t.groups),t.groups}}catch(a){console.error("Error getting groups from localStorage:",a)}return[]}})(e.id);return console.log("Fetched groups for user:",r),{...e,groups:r}}catch(t){return console.error("Error updating user with groups:",t),e}};Object(a.useEffect)(()=>{(async()=>{try{if(console.log("Checking authentication status..."),d()){if(console.log("User authenticated via cookies"),r&&r.id){const e=await m(r);n(e)}return void c(!1)}const l=localStorage.getItem("authToken"),s=localStorage.getItem("userData");if(console.log("CheckAuth - Token exists:",!!l),console.log("CheckAuth - UserData exists:",!!s),l&&s)try{const r=JSON.parse(s);console.log("CheckAuth - Parsed user data:",r);try{if((await D("http://localhost:8000/users/me",{method:"GET",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}})).ok){console.log("CheckAuth - Token is valid, setting user:",r);const e=await m(r);n(e),i(!0)}else console.log("Invalid token detected, clearing auth data"),u()}catch(e){console.log("Token validation error, but continuing:",e),console.log("CheckAuth - Using cached user data due to network error:",r);try{const e=await m(r);n(e)}catch(t){console.error("Error fetching groups in offline mode:",t),n(r)}i(!0)}}catch(a){console.log("Error parsing user data from localStorage:",a),u()}}catch(o){console.log("Authentication error, clearing auth data:",o),u()}finally{c(!1)}})()},[]);const u=()=>{try{localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userData");try{oe("user_data",{path:"/"}),oe("access_token",{path:"/"}),oe("refresh_token",{path:"/"})}catch(e){console.log("Error clearing cookies, but continuing:",e)}}catch(t){console.log("Error clearing auth data, but continuing:",t)}finally{n(null),i(!1)}},g={user:r,isAuthenticated:l,loading:s,login:async(e,t)=>{try{const a=await B(e,t);if(console.log("Login API response:",a),a&&a.access_token){localStorage.setItem("authToken",a.access_token),a.refresh_token&&localStorage.setItem("refreshToken",a.refresh_token);const e={id:a.user_id,username:a.username,role:a.role};console.log("User data to be stored:",e);const t=await m(e);return console.log("User data with groups:",t),localStorage.setItem("userData",JSON.stringify(t)),n(t),i(!0),{success:!0,user:t}}return{success:!1,error:"Invalid response from server"}}catch(r){return console.error("Login error:",r),{success:!1,error:"string"===typeof r?r:"An error occurred during login"}}},register:async(e,t)=>{try{const a=await k(e,t);if(a&&a.access_token){localStorage.setItem("authToken",a.access_token),a.refresh_token&&localStorage.setItem("refreshToken",a.refresh_token);const e={id:a.user_id,username:a.username,role:a.role},t=await m(e);return localStorage.setItem("userData",JSON.stringify(t)),n(t),i(!0),{success:!0}}return{success:!1,error:"Invalid response from server"}}catch(r){return console.error("Registration error:",r),{success:!1,error:"string"===typeof r?r:"An error occurred during registration"}}},logout:async()=>{try{await ae()}catch(e){console.error("Error calling logout API:",e)}finally{u()}}};return o.a.createElement(ne.Provider,{value:g},t)},ie=()=>{const e=Object(a.useContext)(ne);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e};var se=r(670),ce=r(671),de=r(672);var me={getNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{const a=await fetch(`${F}/notifications?limit=${e}&include_read=${t}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(a)}catch(r){throw console.error("Error fetching notifications:",r),r}},getUnreadCount:async()=>{try{const t=await fetch(`${F}/notifications/unread-count`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(t)}catch(e){throw console.error("Error fetching unread count:",e),e}},checkNewNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const r=e?`${F}/notifications/check-new?last_checked=${encodeURIComponent(e)}`:`${F}/notifications/check-new`,a=await fetch(r,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(a)}catch(t){return console.error("Error checking for new notifications:",t),{has_new:!1}}},markAsRead:async e=>{try{const r=await fetch(`${F}/notifications/${e}`,{method:"PUT",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify({is_read:!0})});return await C(r)}catch(t){throw console.error("Error marking notification as read:",t),t}},markAllAsRead:async()=>{try{const t=await fetch(`${F}/notifications/mark-all-read`,{method:"PUT",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(t)}catch(e){throw console.error("Error marking all notifications as read:",e),e}},deleteNotification:async e=>{try{const r=await fetch(`${F}/notifications/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(r)}catch(t){throw console.error("Error deleting notification:",t),t}},createNotification:async e=>{try{const r=await fetch(`${F}/notifications`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"},body:JSON.stringify(e)});return await C(r)}catch(t){throw console.error("Error creating notification:",t),t}},getDashboardAnalytics:async()=>{try{const t=await fetch(`${F}/analytics/dashboard`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(t)}catch(e){throw console.error("Error fetching dashboard analytics:",e),e}},getNotificationStats:async()=>{try{const t=await fetch(`${F}/analytics/notification-stats`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(t)}catch(e){throw console.error("Error fetching notification statistics:",e),e}},getUserActivities:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{const a=await fetch(`${F}/analytics/user-activities/${e}?limit=${t}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(a)}catch(r){throw console.error("Error fetching user activities:",r),r}},getDocumentActivities:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{const a=await fetch(`${F}/analytics/document-activities/${e}?limit=${t}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return await C(a)}catch(r){throw console.error("Error fetching document activities:",r),r}}};const ue=Object(a.createContext)(),ge=()=>Object(a.useContext)(ue),pe=e=>{let{children:t}=e;const{isAuthenticated:r,user:n}=ie(),[l,i]=Object(a.useState)([]),[s,c]=Object(a.useState)(0),[d,m]=Object(a.useState)(!1),[u,g]=Object(a.useState)(null),[p,h]=Object(a.useState)(!1),f=Object(a.useRef)(null),y=Object(a.useCallback)(async()=>{if(r)try{const e=await me.getUnreadCount();e&&void 0!==e.count&&c(e.count)}catch(u){console.error("Error fetching unread count:",u)}},[r]),E=Object(a.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(r)try{t||m(!0),g(null),f.current=(new Date).toISOString();const r=await me.getNotifications(20,e);r&&r.notifications&&(i(r.notifications),t||h(!1))}catch(u){console.error("Error fetching notifications:",u),t||g("Failed to fetch notifications")}finally{t||m(!1)}},[r]),b=Object(a.useCallback)(async()=>{if(r)try{const t=await me.checkNewNotifications(f.current);t&&t.has_new?(h(!0),await y(),await E(!0,!0)):await y(),f.current=(new Date).toISOString()}catch(u){console.error("Error checking for new notifications:",u);try{await y()}catch(e){console.error("Error fetching unread count:",e)}}},[r,y,E]);Object(a.useEffect)(()=>{if(!r)return;return(async()=>{try{await y(),await E(!0,!0)}catch(u){console.error("Error during initial notification fetch:",u)}})(),()=>{}},[r,E,y,b]);const v=Object(a.useCallback)(()=>{h(!1)},[]),w={notifications:l,unreadCount:s,loading:d,error:u,hasNewNotifications:p,fetchNotifications:E,fetchUnreadCount:y,checkForNewNotifications:b,markAsRead:async e=>{try{return m(!0),await me.markAsRead(e),i(t=>t.map(t=>t.id===e?{...t,is_read:!0}:t)),y(),!0}catch(u){return console.error("Error marking notification as read:",u),g("Failed to mark notification as read"),!1}finally{m(!1)}},markAllAsRead:async()=>{try{return m(!0),await me.markAllAsRead(),i(e=>e.map(e=>({...e,is_read:!0}))),c(0),!0}catch(u){return console.error("Error marking all notifications as read:",u),g("Failed to mark all notifications as read"),!1}finally{m(!1)}},deleteNotification:async e=>{try{return m(!0),await me.deleteNotification(e),i(t=>t.filter(t=>t.id!==e)),y(),!0}catch(u){return console.error("Error deleting notification:",u),g("Failed to delete notification"),!1}finally{m(!1)}},createNotification:async e=>{if(!r||"admin"!==(null===n||void 0===n?void 0:n.role))return g("Only admins can create notifications"),!1;try{m(!0);const t=await me.createNotification(e);return E(),t}catch(u){return console.error("Error creating notification:",u),g("Failed to create notification"),!1}finally{m(!1)}},fetchDashboardAnalytics:async()=>{if(!r||"admin"!==(null===n||void 0===n?void 0:n.role))return g("Only admins can access dashboard analytics"),null;try{return m(!0),await me.getDashboardAnalytics()}catch(u){return console.error("Error fetching dashboard analytics:",u),g("Failed to fetch dashboard analytics"),null}finally{m(!1)}},resetNewNotificationsFlag:v};return o.a.createElement(ue.Provider,{value:w},t)};r(394);var he=()=>{const e=h(),{notifications:t,unreadCount:r,loading:n,hasNewNotifications:l,fetchNotifications:i,fetchUnreadCount:s,markAsRead:c,markAllAsRead:d,deleteNotification:m,resetNewNotificationsFlag:u,checkForNewNotifications:g}=ge(),p=Object(a.useMemo)(()=>{const e=new Map;return t.filter(t=>{const r=`${t.document_id}-${t.type}`;return!e.has(r)&&(e.set(r,!0),!0)})},[t]),f=Object(a.useMemo)(()=>p.filter(e=>!e.is_read).length,[p]),[y,E]=Object(a.useState)(!1),b=Object(a.useRef)(null);return Object(a.useEffect)(()=>{const e=e=>{b.current&&!b.current.contains(e.target)&&E(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),o.a.createElement("div",{className:"notification-bell-container",ref:b},o.a.createElement("button",{className:`notification-bell-button ${l?"has-new-notifications":""}`,onClick:()=>{y||(g&&g(),i(!0),u()),E(!y)},"aria-label":"Notifications"},o.a.createElement(se.a,{className:`notification-bell-icon ${l?"pulse":""}`}),f>0&&o.a.createElement("span",{className:"notification-badge"},f)),y&&o.a.createElement("div",{className:`notification-dropdown ${e.darkMode?"dark":""}`},o.a.createElement("div",{className:"notification-header"},o.a.createElement("h3",null,"Notifications"),p.length>0&&o.a.createElement("button",{className:"mark-all-read-button",onClick:async e=>{e.stopPropagation();try{await d(),i(!0),s()}catch(t){console.error("Error marking all notifications as read:",t)}},disabled:n},"Mark all as read")),o.a.createElement("div",{className:"notification-list"},n&&o.a.createElement("div",{className:"notification-loading"},"Loading..."),!n&&0===p.length&&o.a.createElement("div",{className:"notification-empty"},"No notifications"),!n&&p.map(e=>o.a.createElement("div",{key:e.id,className:`notification-item ${e.is_read?"read":"unread"}`},o.a.createElement("div",{className:"notification-icon"},(e=>{switch(e){case"document_added":return"\ud83d\udcc4";case"document_deleted":return"\ud83d\uddd1\ufe0f";case"system_reset":return"\ud83d\udd04";case"user_added":return"\ud83d\udc64";default:return"\ud83d\udd14"}})(e.type)),o.a.createElement("div",{className:"notification-content"},o.a.createElement("div",{className:"notification-title"},e.title),o.a.createElement("div",{className:"notification-message"},e.message),o.a.createElement("div",{className:"notification-meta"},o.a.createElement("span",{className:"notification-time"},(e=>new Date(e).toLocaleString())(e.created_at)),e.creator_name&&o.a.createElement("span",{className:"notification-creator"},"by ",e.creator_name))),o.a.createElement("div",{className:"notification-actions"},!e.is_read&&o.a.createElement("button",{className:"notification-action-button",onClick:t=>(async(e,t)=>{t.stopPropagation();try{await c(e),i(!0),s()}catch(r){console.error("Error marking notification as read:",r)}})(e.id,t),title:"Mark as read"},o.a.createElement(ce.a,{className:"notification-action-icon"})),o.a.createElement("button",{className:"notification-action-button",onClick:t=>(async(e,t)=>{t.stopPropagation();try{await m(e),i(!0),s()}catch(r){console.error("Error deleting notification:",r)}})(e.id,t),title:"Delete"},o.a.createElement(de.a,{className:"notification-action-icon"}))))))))};var fe=e=>{let{toggleSidebar:t}=e;const[r,n]=Object(a.useState)(!1),{logout:l,user:s}=ie(),c=h(),d=Object(i.r)();console.log("TopBar - Theme state:",{darkMode:c.darkMode,toggleFunction:typeof c.toggleDarkMode,themeColors:c.colors});return o.a.createElement("header",{className:"topbar"},o.a.createElement("div",{style:{display:"flex",alignItems:"center"}},o.a.createElement("button",{className:"mobile-menu-btn",onClick:t,style:{background:"none",border:"none",cursor:"pointer",marginRight:"1rem"}},o.a.createElement(E.a,{style:{height:"1.5rem",width:"1.5rem"}})),o.a.createElement("h1",{style:{fontSize:"1.25rem",fontWeight:600,display:"none"},className:"sm-visible"},"Admin Dashboard")),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"1rem"}},o.a.createElement("button",{style:{background:"none",border:"none",cursor:"pointer",padding:"0.5rem",borderRadius:"9999px"},onClick:()=>{console.log("Theme toggle clicked, current darkMode:",c.darkMode),c.toggleDarkMode(),console.log("After toggle, darkMode should be:",!c.darkMode)},"aria-label":"Toggle dark mode"},c.darkMode?o.a.createElement(b.a,{style:{height:"1.25rem",width:"1.25rem",color:"#FBBF24"}}):o.a.createElement(v.a,{style:{height:"1.25rem",width:"1.25rem",color:"#6B7280"}})),o.a.createElement(he,null),o.a.createElement("div",{style:{position:"relative"}},o.a.createElement("button",{style:{display:"flex",alignItems:"center",gap:"0.5rem",background:"none",border:"none",cursor:"pointer"},onClick:()=>n(!r)},o.a.createElement("div",{style:{height:"2rem",width:"2rem",borderRadius:"9999px",backgroundColor:c.colors.background.tertiary,display:"flex",alignItems:"center",justifyContent:"center"}},o.a.createElement(w.a,{style:{height:"1.75rem",width:"1.75rem",color:c.colors.text.tertiary}})),o.a.createElement("span",{style:{display:"none",fontSize:"0.875rem",fontWeight:500},className:"md-visible"},(null===s||void 0===s?void 0:s.username)||"User")),r&&o.a.createElement("div",{style:{position:"absolute",right:0,marginTop:"0.5rem",width:"12rem",borderRadius:"0.375rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",backgroundColor:c.colors.background.primary,border:`1px solid ${c.colors.border.primary}`,zIndex:50}},o.a.createElement("div",{style:{padding:"0.75rem 1rem",borderBottom:`1px solid ${c.colors.border.primary}`}},o.a.createElement("p",{style:{fontSize:"0.875rem",fontWeight:500,color:c.colors.text.primary}},(null===s||void 0===s?void 0:s.username)||"User"),o.a.createElement("p",{style:{fontSize:"0.75rem",color:c.colors.text.tertiary,marginTop:"0.25rem"}},(null===s||void 0===s?void 0:s.role)||"user")),o.a.createElement("a",{href:"/admin/profile",style:{display:"block",padding:"0.5rem 1rem",fontSize:"0.875rem",textDecoration:"none",color:c.colors.text.primary,transition:"background-color 0.2s",":hover":{backgroundColor:c.colors.background.secondary}}},"Your Profile"),o.a.createElement("button",{style:{display:"flex",alignItems:"center",width:"100%",textAlign:"left",padding:"0.5rem 1rem",fontSize:"0.875rem",color:c.colors.error.primary,background:"none",border:"none",cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>{l(),d("/login"),console.log("Logged out successfully")}},o.a.createElement(x.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem"}}),"Sign out")))))};var ye=()=>{const e=h(),[t,r]=Object(a.useState)(!0);return o.a.createElement("div",{className:"app-container",style:{overflow:"hidden",backgroundColor:e.colors.background.primary,color:e.colors.text.primary}},o.a.createElement(y,{sidebarOpen:t,setSidebarOpen:r}),o.a.createElement("div",{className:"main-content",style:{overflow:"hidden",maxWidth:"100%",backgroundColor:e.colors.background.primary}},o.a.createElement(fe,{toggleSidebar:()=>{r(!t)}}),o.a.createElement("main",{style:{overflow:"hidden",maxWidth:"100%",padding:"0",backgroundColor:e.colors.background.primary}},o.a.createElement(i.b,null))))},Ee=r(678),be=r(679);var ve=()=>{const[e,t]=Object(a.useState)(!1),[r,n]=Object(a.useState)(!1),[l,c]=Object(a.useState)(!0),{logout:m,user:u}=ie(),g=Object(i.r)();Object(i.p)();Object(a.useEffect)(()=>{window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches&&(t(!0),document.body.classList.add("dark"));const e=localStorage.getItem("darkMode");if(e){const r="true"===e;t(r),r?document.body.classList.add("dark"):document.body.classList.remove("dark")}},[]);return o.a.createElement("div",{className:"user-container",style:{display:"flex",flexDirection:"column",height:"100vh",backgroundColor:e?"#111827":"#F9FAFB"}},o.a.createElement("header",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"1rem",backgroundColor:e?"#1F2937":"#FFFFFF",borderBottom:`1px solid ${e?"#374151":"#E5E7EB"}`,boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center"}},o.a.createElement("img",{src:"/logo.svg",alt:"Logo",style:{height:"2rem",width:"2rem",marginRight:"0.75rem"}}),o.a.createElement("h1",{style:{fontSize:"1.25rem",fontWeight:600,color:e?"#F9FAFB":"#111827"}},"Document AI Chat")),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"1rem"}},o.a.createElement(he,{darkMode:e}),o.a.createElement("div",{style:{position:"relative"}},o.a.createElement("button",{style:{display:"flex",alignItems:"center",gap:"0.5rem",background:"none",border:"none",cursor:"pointer"},onClick:()=>n(!r)},o.a.createElement("div",{style:{height:"2rem",width:"2rem",borderRadius:"9999px",backgroundColor:e?"#374151":"#E5E7EB",display:"flex",alignItems:"center",justifyContent:"center"}},o.a.createElement(w.a,{style:{height:"1.75rem",width:"1.75rem",color:e?"#9CA3AF":"#6B7280"}})),o.a.createElement("span",{style:{fontSize:"0.875rem",fontWeight:500,color:e?"#F9FAFB":"#111827"}},(null===u||void 0===u?void 0:u.username)||"User")),r&&o.a.createElement("div",{style:{position:"absolute",right:0,marginTop:"0.5rem",width:"12rem",borderRadius:"0.375rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",backgroundColor:e?"#1F2937":"#FFFFFF",border:`1px solid ${e?"#374151":"#E5E7EB"}`,zIndex:50}},o.a.createElement("div",{style:{padding:"0.75rem 1rem",borderBottom:`1px solid ${e?"#374151":"#E5E7EB"}`}},o.a.createElement("p",{style:{fontSize:"0.875rem",fontWeight:500,color:e?"#F9FAFB":"#111827"}},(null===u||void 0===u?void 0:u.username)||"User"),o.a.createElement("p",{style:{fontSize:"0.75rem",color:e?"#9CA3AF":"#6B7280",marginTop:"0.25rem"}},(null===u||void 0===u?void 0:u.role)||"user")),o.a.createElement("button",{style:{display:"flex",alignItems:"center",width:"100%",textAlign:"left",padding:"0.5rem 1rem",fontSize:"0.875rem",color:e?"#F87171":"#DC2626",background:"none",border:"none",cursor:"pointer"},onClick:()=>{m(),g("/login"),console.log("Logged out successfully")}},o.a.createElement(Ee.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem"}}),"Sign out"))))),o.a.createElement("div",{style:{display:"flex",flex:1,overflow:"hidden"}},o.a.createElement("aside",{style:{width:l?"250px":"0",backgroundColor:e?"#1F2937":"#FFFFFF",borderRight:`1px solid ${e?"#374151":"#E5E7EB"}`,transition:"width 0.3s ease",overflow:"hidden"}},o.a.createElement("nav",{style:{padding:"1rem",display:"flex",flexDirection:"column",gap:"0.5rem"}},o.a.createElement(s.b,{to:"/chat",style:t=>{let{isActive:r}=t;return{display:"flex",alignItems:"center",gap:"0.75rem",padding:"0.75rem 1rem",borderRadius:"0.375rem",backgroundColor:r?e?"#374151":"#F3F4F6":"transparent",color:r?e?"#F9FAFB":"#111827":e?"#D1D5DB":"#6B7280",textDecoration:"none",fontWeight:r?"500":"normal"}},end:!0},o.a.createElement(d.a,{style:{height:"1.25rem",width:"1.25rem"}}),o.a.createElement("span",null,"Chat")),o.a.createElement(s.b,{to:"/chat/documents",style:t=>{let{isActive:r}=t;return{display:"flex",alignItems:"center",gap:"0.75rem",padding:"0.75rem 1rem",borderRadius:"0.375rem",backgroundColor:r?e?"#374151":"#F3F4F6":"transparent",color:r?e?"#F9FAFB":"#111827":e?"#D1D5DB":"#6B7280",textDecoration:"none",fontWeight:r?"500":"normal"}}},o.a.createElement(be.a,{style:{height:"1.25rem",width:"1.25rem"}}),o.a.createElement("span",null,"Documents")))),o.a.createElement("main",{style:{flex:1,padding:"1rem",overflow:"auto"}},o.a.createElement(i.b,null))),o.a.createElement("footer",{style:{padding:"0.5rem 1rem",fontSize:"0.75rem",color:e?"#9CA3AF":"#6B7280",textAlign:"center",borderTop:`1px solid ${e?"#374151":"#E5E7EB"}`,backgroundColor:e?"#1F2937":"#FFFFFF"}},o.a.createElement("p",null,"\xa9 2025 Document AI Chat")))},we=r(723),xe=r(719),Fe=r(739),De=r(724),Ce=r(711),Se=r(740),Be=r(357),ke=r(713),Ae=r(690),je=r(354),Oe=r.n(je),Ne=r(355),ze=r.n(Ne),Te=r(356),$e=r.n(Te),Ie=r(708),Re=r(727),We=r(721),_e=r(725),Pe=r(726),Le=r(689),Ue=r(686),Me=r(107),Ge=r.n(Me),He=r(161),Je=r.n(He),Xe=r(351),Ye=r.n(Xe),Ve=r(105),qe=r.n(Ve),Ke=r(106),Qe=r.n(Ke),Ze=r(682),et=r(718),tt=r(337),rt=r(160),at=r(84),ot=r(113);var nt=e=>{let{readCount:t,unreadCount:r}=e;const a=[{name:"Read",value:t,color:"#4caf50"},{name:"Unread",value:r,color:"#f44336"}],n=t+r,l=Math.round(t/(n||1)*100),i=["#4caf50","#f44336"];return o.a.createElement(we.a,{sx:{height:300,position:"relative"}},o.a.createElement(Ze.a,{width:"100%",height:"100%"},o.a.createElement(et.a,null,o.a.createElement(tt.a,{data:a,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,dataKey:"value"},a.map((e,t)=>o.a.createElement(rt.a,{key:`cell-${t}`,fill:i[t%i.length]}))),o.a.createElement(at.a,{content:o.a.createElement(e=>{let{active:t,payload:r}=e;if(t&&r&&r.length){const e=r[0];return o.a.createElement(we.a,{sx:{bgcolor:"background.paper",p:1.5,boxShadow:1,borderRadius:1}},o.a.createElement(De.a,{variant:"body2",fontWeight:"medium"},`${e.name}: ${e.value}`),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},`${(e.value/n*100).toFixed(1)}%`))}return null},null)}),o.a.createElement(ot.a,null))),o.a.createElement(we.a,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",display:"flex",flexDirection:"column",alignItems:"center"}},o.a.createElement(De.a,{variant:"h4",component:"div",fontWeight:"bold"},l,"%"),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Read")))},lt=r(683),it=r(684),st=r(340),ct=r(341),dt=r(165);var mt=e=>{let{userData:t}=e;const r=[...t].sort((e,t)=>t.readPercentage-e.readPercentage).slice(0,5);return o.a.createElement(we.a,{sx:{height:300}},o.a.createElement(Ze.a,{width:"100%",height:"100%"},o.a.createElement(lt.a,{data:r,layout:"vertical",margin:{top:5,right:30,left:20,bottom:5}},o.a.createElement(it.a,{strokeDasharray:"3 3",horizontal:!1}),o.a.createElement(st.a,{type:"number",domain:[0,100]}),o.a.createElement(ct.a,{type:"category",dataKey:"username",width:100,tick:{fontSize:12}}),o.a.createElement(at.a,{content:o.a.createElement(e=>{let{active:t,payload:r}=e;if(t&&r&&r.length){const e=r[0].payload;return o.a.createElement(we.a,{sx:{bgcolor:"background.paper",p:1.5,boxShadow:1,borderRadius:1}},o.a.createElement(De.a,{variant:"body2",fontWeight:"medium"},e.username),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Read: ",e.read," (",e.readPercentage,"%)"),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Unread: ",e.unread),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Total: ",e.total))}return null},null)}),o.a.createElement(dt.a,{dataKey:"readPercentage",name:"Read Percentage",fill:"#3f51b5",radius:[0,4,4,0],label:{position:"right",formatter:e=>`${e}%`,fontSize:12}}))))};var ut=e=>{let{notifications:t}=e;const r={};t.forEach(e=>{const t=e.type||"unknown";r[t]=(r[t]||0)+1});const a=Object.entries(r).map(e=>{let[t,r]=e;return{type:t.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),count:r,color:n(t)}});function n(e){switch(e){case"document_added":return"#4caf50";case"document_deleted":return"#f44336";case"document_updated":return"#ff9800";default:return"#2196f3"}}return o.a.createElement(we.a,{sx:{height:300}},o.a.createElement(Ze.a,{width:"100%",height:"100%"},o.a.createElement(lt.a,{data:a,margin:{top:5,right:30,left:20,bottom:5}},o.a.createElement(it.a,{strokeDasharray:"3 3",vertical:!1}),o.a.createElement(st.a,{dataKey:"type",tick:{fontSize:12},interval:0,angle:-45,textAnchor:"end",height:80}),o.a.createElement(ct.a,null),o.a.createElement(at.a,{content:o.a.createElement(e=>{let{active:r,payload:a}=e;if(r&&a&&a.length){const e=a[0].payload;return o.a.createElement(we.a,{sx:{bgcolor:"background.paper",p:1.5,boxShadow:1,borderRadius:1}},o.a.createElement(De.a,{variant:"body2",fontWeight:"medium"},e.type),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Count: ",e.count),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Percentage: ",Math.round(e.count/t.length*100),"%"))}return null},null)}),o.a.createElement(dt.a,{dataKey:"count",name:"Count",radius:[4,4,0,0],label:{position:"top",fontSize:12}},a.map((e,t)=>o.a.createElement(rt.a,{key:`cell-${t}`,fill:e.color}))))))},gt=r(720),pt=r(728),ht=r(345),ft=r.n(ht),yt=r(344),Et=r.n(yt),bt=r(209),vt=r.n(bt),wt=r(346),xt=r.n(wt),Ft=r(342),Dt=r.n(Ft),Ct=r(343),St=r.n(Ct);var Bt=e=>{let{notification:t,documentName:r}=e;const[n,l]=Object(a.useState)(!1),i=()=>{switch(t.type){case"document_added":return"success";case"document_deleted":return"error";case"document_updated":return"warning";default:return"primary"}};return o.a.createElement(_e.a,{sx:{transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-2px)",boxShadow:3}}},o.a.createElement(Pe.a,{sx:{pb:1}},o.a.createElement(we.a,{sx:{display:"flex",alignItems:"flex-start"}},o.a.createElement(we.a,{sx:{p:1,borderRadius:"50%",bgcolor:`${i()}.light`,mr:2,display:"flex",alignItems:"center",justifyContent:"center"}},(()=>{switch(t.type){case"document_added":return o.a.createElement(vt.a,{sx:{color:"#4caf50"}});case"document_deleted":return o.a.createElement(Dt.a,{sx:{color:"#f44336"}});case"document_updated":return o.a.createElement(St.a,{sx:{color:"#ff9800"}});default:return o.a.createElement(Je.a,{sx:{color:"#2196f3"}})}})()),o.a.createElement(we.a,{sx:{flexGrow:1}},o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"}},o.a.createElement(De.a,{variant:"subtitle1",component:"div"},t.title),o.a.createElement(we.a,{sx:{display:"flex",alignItems:"center"}},t.is_read?o.a.createElement(qe.a,{color:"success",fontSize:"small",sx:{mr:.5}}):o.a.createElement(Qe.a,{color:"error",fontSize:"small",sx:{mr:.5}}),o.a.createElement(Re.a,{size:"small",onClick:()=>{l(!n)},"aria-expanded":n,"aria-label":"show more"},n?o.a.createElement(Et.a,null):o.a.createElement(ft.a,null)))),o.a.createElement(we.a,{sx:{display:"flex",flexWrap:"wrap",alignItems:"center",mt:.5,gap:1}},o.a.createElement(we.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(xt.a,{fontSize:"small",sx:{mr:.5,color:"text.secondary",fontSize:16}}),o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},(e=>e?new Date(e).toLocaleString():"")(t.created_at))),r&&o.a.createElement(gt.a,{icon:o.a.createElement(vt.a,null),label:r,size:"small",variant:"outlined"})))),o.a.createElement(pt.a,{in:n,timeout:"auto",unmountOnExit:!0},o.a.createElement(we.a,{sx:{mt:2}},o.a.createElement(Ue.a,null),o.a.createElement(De.a,{variant:"body2",sx:{mt:2}},t.message||"No additional details available."),o.a.createElement(we.a,{sx:{mt:2,display:"flex",alignItems:"center"}},o.a.createElement(gt.a,{label:(e=>e?e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):"Unknown")(t.type),size:"small",color:i()}))))))};var kt=e=>{let{stats:t,refreshData:r,isRefreshing:n}=e;const[l,i]=Object(a.useState)(!0),s=Object(a.useMemo)(()=>{const e=new Map;return t.notifications.filter(t=>{const r=`${t.document_id}-${t.type}`;return!e.has(r)&&(e.set(r,!0),!0)})},[t.notifications]),c=e=>{var r,a;return(null===(r=t.documents)||void 0===r?void 0:null===(a=r.find(t=>(null===t||void 0===t?void 0:t.id)===e))||void 0===a?void 0:a.filename)||"N/A"},d=(()=>{const e={};return t.users.forEach(t=>{e[t.id]={id:t.id,username:t.username,read:0,unread:0,total:s.length}}),t.read_status.forEach(t=>{const r=s.some(e=>e.id===t.notification_id);e[t.user_id]&&r&&(e[t.user_id].read+=1,e[t.user_id].unread=e[t.user_id].total-e[t.user_id].read)}),Object.values(e).map(e=>({...e,readPercentage:Math.round(e.read/e.total*100)}))})();return o.a.createElement(we.a,null,o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3}},o.a.createElement(De.a,{variant:"h5",component:"h2"},"Notification Read Status"),o.a.createElement(Ie.a,{title:"Refresh data"},o.a.createElement(Re.a,{onClick:()=>{i(!1),r(),setTimeout(()=>{i(!0)},300)},disabled:n,sx:{animation:n?"spin 1s linear infinite":"none","@keyframes spin":{"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}}},o.a.createElement(Ge.a,null)))),o.a.createElement(We.a,{container:!0,spacing:3,sx:{mb:4}},o.a.createElement(We.a,{item:!0,xs:12,sm:6,md:3},o.a.createElement(_e.a,{sx:{borderLeft:"4px solid #3f51b5",transition:"transform 0.2s","&:hover":{transform:"scale(1.02)"}}},o.a.createElement(Pe.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(we.a,{sx:{p:1.5,borderRadius:"50%",bgcolor:"rgba(63, 81, 181, 0.1)",mr:2}},o.a.createElement(Je.a,{color:"primary"})),o.a.createElement(we.a,null,o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Total Notifications"),o.a.createElement(De.a,{variant:"h5",component:"div"},t.summary.total_notifications))))),o.a.createElement(We.a,{item:!0,xs:12,sm:6,md:3},o.a.createElement(_e.a,{sx:{borderLeft:"4px solid #9c27b0",transition:"transform 0.2s","&:hover":{transform:"scale(1.02)"}}},o.a.createElement(Pe.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(we.a,{sx:{p:1.5,borderRadius:"50%",bgcolor:"rgba(156, 39, 176, 0.1)",mr:2}},o.a.createElement(Ye.a,{sx:{color:"#9c27b0"}})),o.a.createElement(we.a,null,o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Total Users"),o.a.createElement(De.a,{variant:"h5",component:"div"},t.summary.total_users))))),o.a.createElement(We.a,{item:!0,xs:12,sm:6,md:3},o.a.createElement(_e.a,{sx:{borderLeft:"4px solid #4caf50",transition:"transform 0.2s","&:hover":{transform:"scale(1.02)"}}},o.a.createElement(Pe.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(we.a,{sx:{p:1.5,borderRadius:"50%",bgcolor:"rgba(76, 175, 80, 0.1)",mr:2}},o.a.createElement(qe.a,{sx:{color:"#4caf50"}})),o.a.createElement(we.a,null,o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Read Count"),o.a.createElement(De.a,{variant:"h5",component:"div"},t.summary.read_count))))),o.a.createElement(We.a,{item:!0,xs:12,sm:6,md:3},o.a.createElement(_e.a,{sx:{borderLeft:"4px solid #f44336",transition:"transform 0.2s","&:hover":{transform:"scale(1.02)"}}},o.a.createElement(Pe.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(we.a,{sx:{p:1.5,borderRadius:"50%",bgcolor:"rgba(244, 67, 54, 0.1)",mr:2}},o.a.createElement(Qe.a,{sx:{color:"#f44336"}})),o.a.createElement(we.a,null,o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},"Unread Count"),o.a.createElement(De.a,{variant:"h5",component:"div"},t.summary.unread_count)))))),o.a.createElement(Le.a,{in:l,timeout:500},o.a.createElement(We.a,{container:!0,spacing:3,sx:{mb:4}},o.a.createElement(We.a,{item:!0,xs:12,md:6},o.a.createElement(_e.a,{sx:{height:"100%"}},o.a.createElement(Pe.a,null,o.a.createElement(De.a,{variant:"h6",gutterBottom:!0},"Read Status Overview"),o.a.createElement(nt,{readCount:t.summary.read_count,unreadCount:t.summary.unread_count})))),o.a.createElement(We.a,{item:!0,xs:12,md:6},o.a.createElement(_e.a,{sx:{height:"100%"}},o.a.createElement(Pe.a,null,o.a.createElement(De.a,{variant:"h6",gutterBottom:!0},"Top Users by Read Status"),o.a.createElement(mt,{userData:d})))))),o.a.createElement(Le.a,{in:l,timeout:500,style:{transitionDelay:"150ms"}},o.a.createElement(_e.a,{sx:{mb:4}},o.a.createElement(Pe.a,null,o.a.createElement(De.a,{variant:"h6",gutterBottom:!0},"Notification Type Distribution"),o.a.createElement(ut,{notifications:s})))),o.a.createElement(Le.a,{in:l,timeout:500,style:{transitionDelay:"300ms"}},o.a.createElement(_e.a,null,o.a.createElement(Pe.a,null,o.a.createElement(De.a,{variant:"h6",gutterBottom:!0},"Recent Notifications"),o.a.createElement(Ue.a,{sx:{mb:2}}),o.a.createElement(we.a,{sx:{display:"flex",flexDirection:"column",gap:2}},s.slice(0,5).map(e=>o.a.createElement(Bt,{key:e.id,notification:e,documentName:c(e.document_id)})))))))},At=r(715),jt=r(731),Ot=r(716),Nt=r(709),zt=r(717),Tt=r(732),$t=r(733),It=r(734),Rt=r(735),Wt=r(736),_t=r(737),Pt=r(710),Lt=r(352),Ut=r.n(Lt),Mt=r(353),Gt=r.n(Mt);var Ht=e=>{let{stats:t,refreshData:r,isRefreshing:n}=e;const[l,i]=Object(a.useState)(!1),[s,c]=Object(a.useState)(""),[d,m]=Object(a.useState)(""),[u,g]=Object(a.useState)([]),[p,h]=Object(a.useState)(1),[f,y]=Object(a.useState)(10),E=Object(a.useMemo)(()=>{const e=new Map;return t.notifications.filter(t=>{const r=`${t.document_id}-${t.type}`;return!e.has(r)&&(e.set(r,!0),!0)})},[t.notifications]),b=e=>{switch(e){case"document_added":return"success";case"document_deleted":return"error";case"document_updated":return"warning";default:return"primary"}},v=[...new Set(E.map(e=>e.type))],w=E.filter(e=>!(s&&!e.title.toLowerCase().includes(s.toLowerCase()))&&(!d||e.type===d)),x=w.slice((p-1)*f,(p-1)*f+f);return o.a.createElement(we.a,null,o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3}},o.a.createElement(De.a,{variant:"h5",component:"h2"},"Notification Details"),o.a.createElement(we.a,{sx:{display:"flex",gap:1}},o.a.createElement(Ie.a,{title:"Toggle filters"},o.a.createElement(Re.a,{onClick:()=>i(!l),color:l?"primary":"default"},o.a.createElement(Ut.a,null))),o.a.createElement(Ie.a,{title:"Refresh data"},o.a.createElement(Re.a,{onClick:r,disabled:n,sx:{animation:n?"spin 1s linear infinite":"none","@keyframes spin":{"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}}},o.a.createElement(Ge.a,null))))),l&&o.a.createElement(xe.a,{sx:{p:2,mb:3}},o.a.createElement(We.a,{container:!0,spacing:2},o.a.createElement(We.a,{item:!0,xs:12,md:4},o.a.createElement(At.a,{fullWidth:!0,label:"Search Notifications",variant:"outlined",value:s,onChange:e=>c(e.target.value),InputProps:{startAdornment:o.a.createElement(Gt.a,{sx:{color:"action.active",mr:1}})}})),o.a.createElement(We.a,{item:!0,xs:12,md:4},o.a.createElement(jt.a,{fullWidth:!0,variant:"outlined"},o.a.createElement(Ot.a,{id:"notification-type-label"},"Notification Type"),o.a.createElement(Nt.a,{labelId:"notification-type-label",value:d,onChange:e=>m(e.target.value),label:"Notification Type"},o.a.createElement(zt.a,{value:""},"All Types"),v.map(e=>o.a.createElement(zt.a,{key:e,value:e},e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")))))),o.a.createElement(We.a,{item:!0,xs:12,md:4},o.a.createElement(jt.a,{fullWidth:!0,variant:"outlined"},o.a.createElement(Ot.a,{id:"user-filter-label"},"Filter Users"),o.a.createElement(Nt.a,{labelId:"user-filter-label",multiple:!0,value:u,onChange:e=>g(e.target.value),label:"Filter Users",renderValue:e=>o.a.createElement(we.a,{sx:{display:"flex",flexWrap:"wrap",gap:.5}},e.map(e=>{const r=t.users.find(t=>t.id===e);return o.a.createElement(gt.a,{key:e,label:r?r.username:e,size:"small"})}))},t.users.map(e=>o.a.createElement(zt.a,{key:e.id,value:e.id},e.username))))))),o.a.createElement(Tt.a,{component:xe.a,sx:{mb:3}},o.a.createElement($t.a,{sx:{minWidth:650},"aria-label":"notification details table"},o.a.createElement(It.a,null,o.a.createElement(Rt.a,null,o.a.createElement(Wt.a,null,"Notification"),o.a.createElement(Wt.a,null,"Document"),o.a.createElement(Wt.a,null,"Type"),o.a.createElement(Wt.a,null,"Created"),t.users.filter(e=>0===u.length||u.includes(e.id)).map(e=>o.a.createElement(Wt.a,{key:e.id,align:"center"},e.username)))),o.a.createElement(_t.a,null,x.map(e=>o.a.createElement(Rt.a,{key:e.id,hover:!0},o.a.createElement(Wt.a,{component:"th",scope:"row"},e.title),o.a.createElement(Wt.a,null,(e=>{var r,a;return(null===(r=t.documents)||void 0===r?void 0:null===(a=r.find(t=>(null===t||void 0===t?void 0:t.id)===e))||void 0===a?void 0:a.filename)||"N/A"})(e.document_id)),o.a.createElement(Wt.a,null,o.a.createElement(gt.a,{label:e.type.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),color:b(e.type),size:"small"})),o.a.createElement(Wt.a,null,(e=>new Date(e).toLocaleString())(e.created_at)),t.users.filter(e=>0===u.length||u.includes(e.id)).map(r=>{const a=((e,r)=>{const a=t.read_status.find(t=>t.notification_id===e&&t.user_id===r);return null!==(null===a||void 0===a?void 0:a.read_at)})(e.id,r.id);return o.a.createElement(Wt.a,{key:`${e.id}-${r.id}`,align:"center"},a?o.a.createElement(qe.a,{color:"success",fontSize:"small"}):o.a.createElement(Qe.a,{color:"error",fontSize:"small"}))})))))),o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"}},o.a.createElement(we.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(De.a,{variant:"body2",color:"text.secondary",sx:{mr:2}},"Rows per page:"),o.a.createElement(jt.a,{variant:"standard",size:"small"},o.a.createElement(Nt.a,{value:f,onChange:e=>{y(parseInt(e.target.value,10)),h(1)},displayEmpty:!0},o.a.createElement(zt.a,{value:5},"5"),o.a.createElement(zt.a,{value:10},"10"),o.a.createElement(zt.a,{value:25},"25"),o.a.createElement(zt.a,{value:50},"50")))),o.a.createElement(Pt.a,{count:Math.ceil(w.length/f),page:p,onChange:(e,t)=>{h(t)},color:"primary"})))},Jt=r(738);var Xt=e=>{let{stats:t,refreshData:r,isRefreshing:n}=e;const[l,i]=Object(a.useState)(1),[s,c]=Object(a.useState)(10),d=Object(a.useMemo)(()=>{const e=new Map;return t.notifications.filter(t=>{const r=`${t.document_id}-${t.type}`;return!e.has(r)&&(e.set(r,!0),!0)})},[t.notifications]),m=(()=>{const e={};return t.users.forEach(t=>{e[t.id]={id:t.id,username:t.username,read:0,unread:0,total:d.length}}),t.read_status.forEach(t=>{const r=d.some(e=>e.id===t.notification_id);e[t.user_id]&&r&&(e[t.user_id].read+=1,e[t.user_id].unread=e[t.user_id].total-e[t.user_id].read)}),Object.values(e).map(e=>({...e,readPercentage:Math.round(e.read/e.total*100)}))})(),u=m.slice((l-1)*s,(l-1)*s+s),g=e=>e>=75?"success":e>=50?"warning":"error";return o.a.createElement(we.a,null,o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3}},o.a.createElement(De.a,{variant:"h5",component:"h2"},"Notification Summary"),o.a.createElement(Ie.a,{title:"Refresh data"},o.a.createElement(Re.a,{onClick:r,disabled:n,sx:{animation:n?"spin 1s linear infinite":"none","@keyframes spin":{"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}}},o.a.createElement(Ge.a,null)))),o.a.createElement(Tt.a,{component:xe.a,sx:{mb:3}},o.a.createElement($t.a,{sx:{minWidth:650},"aria-label":"notification summary table"},o.a.createElement(It.a,null,o.a.createElement(Rt.a,null,o.a.createElement(Wt.a,null,"User"),o.a.createElement(Wt.a,{align:"right"},"Read"),o.a.createElement(Wt.a,{align:"right"},"Unread"),o.a.createElement(Wt.a,{align:"right"},"Total"),o.a.createElement(Wt.a,null,"Read %"))),o.a.createElement(_t.a,null,u.map(e=>o.a.createElement(Rt.a,{key:e.id,hover:!0},o.a.createElement(Wt.a,{component:"th",scope:"row"},e.username),o.a.createElement(Wt.a,{align:"right"},e.read),o.a.createElement(Wt.a,{align:"right"},e.unread),o.a.createElement(Wt.a,{align:"right"},e.total),o.a.createElement(Wt.a,null,o.a.createElement(we.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(we.a,{sx:{width:"100%",mr:1}},o.a.createElement(Jt.a,{variant:"determinate",value:e.readPercentage,color:g(e.readPercentage),sx:{height:10,borderRadius:5}})),o.a.createElement(we.a,{sx:{minWidth:35}},o.a.createElement(De.a,{variant:"body2",color:"text.secondary"},`${e.readPercentage}%`))))))))),o.a.createElement(we.a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"}},o.a.createElement(we.a,{sx:{display:"flex",alignItems:"center"}},o.a.createElement(De.a,{variant:"body2",color:"text.secondary",sx:{mr:2}},"Rows per page:"),o.a.createElement(jt.a,{variant:"standard",size:"small"},o.a.createElement(Nt.a,{value:s,onChange:e=>{c(parseInt(e.target.value,10)),i(1)},displayEmpty:!0},o.a.createElement(zt.a,{value:5},"5"),o.a.createElement(zt.a,{value:10},"10"),o.a.createElement(zt.a,{value:25},"25"),o.a.createElement(zt.a,{value:50},"50")))),o.a.createElement(Pt.a,{count:Math.ceil(m.length/s),page:l,onChange:(e,t)=>{i(t)},color:"primary"})))};function Yt(e){const{children:t,value:r,index:a,...n}=e;return o.a.createElement("div",Object.assign({role:"tabpanel",hidden:r!==a,id:`notification-tabpanel-${a}`,"aria-labelledby":`notification-tab-${a}`},n),r===a&&o.a.createElement(we.a,{sx:{p:3}},t))}var Vt=()=>{const e=h(),t=Object(Be.a)({palette:{mode:e.darkMode?"dark":"light",primary:{main:e.colors.accent.primary},secondary:{main:e.colors.accent.secondary},background:{default:e.colors.background.primary,paper:e.colors.background.secondary},text:{primary:e.colors.text.primary,secondary:e.colors.text.secondary}}}),[r,n]=Object(a.useState)(!0),[l,i]=Object(a.useState)(null),[s,c]=Object(a.useState)(null),[d,m]=Object(a.useState)(0),[u,g]=Object(a.useState)(!1);Object(a.useEffect)(()=>{p()},[]);const p=async()=>{try{n(!0);const t=await me.getNotificationStats();i(t),c(null)}catch(e){console.error("Error fetching notification statistics:",e),c("Failed to load notification statistics")}finally{n(!1)}},f=async()=>{if(!u){g(!0);try{const t=await me.getNotificationStats();i(t),c(null)}catch(e){console.error("Error refreshing notification statistics:",e),c("Failed to refresh notification statistics")}finally{g(!1)}}},y=(e,t)=>{m(t)};return o.a.createElement(ke.a,{theme:t},o.a.createElement(Ae.a,null),r?o.a.createElement(xe.a,{sx:{p:4,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:400}},o.a.createElement(Fe.a,{size:60}),o.a.createElement(De.a,{variant:"h6",sx:{mt:2}},"Loading notification data...")):s?o.a.createElement(xe.a,{sx:{p:4,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:400}},o.a.createElement(De.a,{variant:"h6",color:"error"},s),o.a.createElement(we.a,{sx:{mt:2}},o.a.createElement("button",{onClick:f,style:{padding:"8px 16px",backgroundColor:e.colors.error.primary,color:"white",border:"none",borderRadius:"4px",cursor:"pointer"}},"Try Again"))):l&&l.notifications&&0!==l.notifications.length?o.a.createElement(xe.a,{sx:{width:"100%",overflow:"hidden"}},o.a.createElement(we.a,{sx:{borderBottom:1,borderColor:"divider"}},o.a.createElement(Ce.a,{value:d,onChange:y,"aria-label":"notification tabs",centered:!0},o.a.createElement(Se.a,{icon:o.a.createElement(Oe.a,null),label:"Dashboard",id:"notification-tab-0","aria-controls":"notification-tabpanel-0",sx:{minWidth:120}}),o.a.createElement(Se.a,{icon:o.a.createElement(ze.a,null),label:"Details",id:"notification-tab-1","aria-controls":"notification-tabpanel-1",sx:{minWidth:120}}),o.a.createElement(Se.a,{icon:o.a.createElement($e.a,null),label:"Summary",id:"notification-tab-2","aria-controls":"notification-tabpanel-2",sx:{minWidth:120}}))),o.a.createElement(Yt,{value:d,index:0},o.a.createElement(kt,{stats:l,refreshData:f,isRefreshing:u})),o.a.createElement(Yt,{value:d,index:1},o.a.createElement(Ht,{stats:l,refreshData:f,isRefreshing:u})),o.a.createElement(Yt,{value:d,index:2},o.a.createElement(Xt,{stats:l,refreshData:f,isRefreshing:u}))):o.a.createElement(xe.a,{sx:{p:4,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:400}},o.a.createElement(De.a,{variant:"h6"},"No notification data available")))};var qt=()=>{const e=h();return o.a.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"}},o.a.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"0.5rem"},className:"md-flex-row md-items-center md-justify-between"},o.a.createElement("h1",{style:{fontSize:"1.5rem",fontWeight:"600",color:e.colors.text.primary}},"Dashboard")),o.a.createElement("div",{style:{borderRadius:"0.5rem",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",backgroundColor:e.colors.background.secondary}},o.a.createElement(Vt,null)))},Kt=r(693),Qt=r(692),Zt=r(694),er=r(695);const tr=async()=>{const e=await fetch("http://localhost:8000/clear",{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},rr=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const r=await fetch(`http://localhost:8000/conversations/new${e?`?title=${encodeURIComponent(e)}`:""}`,{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return console.log("Create conversation response status:",r.status),await C(r)}catch(t){throw console.error("Error in createNewConversation API call:",t),t}},ar=async()=>{const e=await fetch("http://localhost:8000/save",{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},or=async()=>{const e=await fetch("http://localhost:8000/process-data-folder",{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},nr=async()=>{const e=await fetch("http://localhost:8000/hard-reset",{method:"POST",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(e)},lr=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const a=await fetch(`http://localhost:8000/conversations?page=${e}&limit=${t}&include_empty=${r}`,{method:"GET",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return C(a)},ir=async e=>{try{const r=await fetch(`http://localhost:8000/conversations/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${S()}`,"Content-Type":"application/json"}});return console.log("Delete conversation response status:",r.status),await C(r)}catch(t){throw console.error("Error in deleteConversation API call:",t),t}},sr=Object(a.createContext)(),cr=e=>{let{children:t}=e;const{user:r,isAuthenticated:n}=ie(),[l,i]=Object(a.useState)([]),[s,c]=Object(a.useState)(!1),[d,m]=Object(a.useState)(null),[u,g]=Object(a.useState)(!1),[p,h]=Object(a.useState)([]),[f,y]=Object(a.useState)(null);Object(a.useEffect)(()=>{n&&(b(),E())},[n]);const E=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{const a=await lr(e,t,r);return console.log("Conversations:",a),a.conversations&&h(1===e?a.conversations:e=>[...e,...a.conversations]),a.conversations||[]}catch(d){return console.error("Error fetching conversations:",d),[]}},b=async()=>{try{c(!0),m(null),i([{id:"system-welcome",sender:"system",text:"Welcome to the Document AI Chat! I can answer questions based on your uploaded documents.",timestamp:(new Date).toISOString()}]);let t=null;try{t=(await K()).project_id,console.log("Using Google project ID from backend:",t)}catch(d){console.error("Error getting Google project ID:",d)}const r={use_openai_embeddings:!1,use_google_embeddings:!0,use_google_llm:!0,google_project_id:t,embedding_model:"all-MiniLM-L6-v2",llm_model_name:"gemini-pro",vector_store_type:"chroma",use_compression:!1,top_k:4,max_memory_messages:10},a=await Q(r);console.log("Chatbot initialized:",a);try{const t=await ee();if(console.log("Recent conversation response:",t),t.conversation){const e=t.conversation;console.log("Most recent conversation:",e),y(e.id),await v(e.id)}else i([{id:"system-welcome",sender:"system",text:"Welcome to Document AI Chat! Ask me anything about your documents.",timestamp:(new Date).toISOString()}])}catch(e){console.error("Error loading conversation history:",e)}return g(!0),{success:!0}}catch(d){return console.error("Error initializing chat:",d),m("Failed to initialize chat. Please try again."),i(t=>[...t,{id:`system-error-${Date.now()}`,sender:"system",text:`Error: ${"string"===typeof d?d:"Failed to initialize chat. Please try again."}`,timestamp:(new Date).toISOString()}]),{success:!1,error:"string"===typeof d?d:"Failed to initialize chat"}}finally{c(!1)}},v=async e=>{try{c(!0);const t=await te(e);if(console.log("Loaded conversation messages:",t),t.messages&&t.messages.length>0){const r=t.messages.map(e=>({id:`${e.role}-${e.id||new Date(e.created_at).getTime()}`,sender:e.role,text:e.content,timestamp:e.created_at,user_id:e.user_id,format:"markdown"}));return i([{id:"system-welcome",sender:"system",text:"Loaded previous conversation.",timestamp:(new Date).toISOString()},...r]),y(e),console.log(`Loaded ${r.length} messages for conversation ${e}`),!0}return i([{id:"system-welcome",sender:"system",text:"This conversation has no messages yet. Start chatting!",timestamp:(new Date).toISOString()}]),!1}catch(d){return console.error("Error loading conversation messages:",d),m("Failed to load conversation messages"),i([{id:`system-error-${Date.now()}`,sender:"system",text:`Error: ${"string"===typeof d?d:"Failed to load conversation messages. Please try again."}`,timestamp:(new Date).toISOString()}]),!1}finally{c(!1)}},w=async()=>{try{c(!0),i([{id:"system-welcome",sender:"system",text:"Starting a new conversation. Ask me anything about your documents!",timestamp:(new Date).toISOString()}]),y(null);try{const e=await rr();if(console.log("New conversation created:",e),!e||!e.conversation)throw new Error("Invalid response from server");y(e.conversation.id)}catch(d){return console.error("Error creating new conversation:",d),i(t=>[...t,{id:`system-error-${Date.now()}`,sender:"system",text:`Failed to create new conversation: ${d.message||"Unknown error"}. Please try again later.`,timestamp:(new Date).toISOString()}]),m(`Failed to create new conversation: ${d.message||"Unknown error"}`),!1}return await tr(),await E(),!0}catch(d){return console.error("Error starting new conversation:",d),m("Failed to start new conversation"),!1}finally{c(!1)}},x={messages:l,loading:s,error:d,initialized:u,conversations:p,currentConversationId:f,sendMessage:async e=>{try{c(!0),m(null);const t={id:`user-${Date.now()}`,sender:"user",text:e,timestamp:(new Date).toISOString(),user_id:null===r||void 0===r?void 0:r.id};if(i(e=>[...e,t]),!f){const t=e.length>50?e.substring(0,47)+"...":e;try{const e=await rr(t);e&&e.conversation&&(y(e.conversation.id),console.log(`Created new conversation with ID: ${e.conversation.id}`))}catch(d){console.error("Error creating new conversation:",d)}}const a=await Z(e);console.log("Chat API response:",a);const o={id:`bot-${Date.now()}`,sender:"bot",text:a.response,timestamp:(new Date).toISOString(),user_id:null,format:a.format||"markdown",document_references:a.document_references||[]};return console.log("Bot message:",o),i(e=>[...e,o]),E(),{success:!0,response:a.response}}catch(d){return console.error("Error sending message:",d),m("Failed to send message"),i(t=>[...t,{id:`system-error-${Date.now()}`,sender:"system",text:`Error: ${"string"===typeof d?d:"Failed to send message. Please try again."}`,timestamp:(new Date).toISOString(),user_id:null}]),{success:!1,error:"string"===typeof d?d:"Failed to send message"}}finally{c(!1)}},clearChat:()=>{i([{id:"system-welcome",sender:"system",text:"Chat history cleared. How can I help you today?",timestamp:(new Date).toISOString()}])},initializeChat:b,formatTimestamp:e=>{const t=new Date(e),r=new Date;if(t.toDateString()===r.toDateString())return t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});const a=new Date(r);if(a.setDate(r.getDate()-1),t.toDateString()===a.toDateString())return`Yesterday, ${t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}`;const o=new Date(r);if(o.setDate(r.getDate()-7),t>o){const e={weekday:"short"};return`${t.toLocaleDateString(void 0,e)}, ${t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}`}return t.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},loadConversation:async e=>{f!==e&&await v(e)},startNewConversation:w,fetchConversations:E,deleteConversation:async e=>{try{return c(!0),await ir(e),f===e&&await w(),await E(),!0}catch(d){return console.error("Error deleting conversation:",d),m("Failed to delete conversation"),!1}finally{c(!1)}}};return o.a.createElement(sr.Provider,{value:x},t)},dr=()=>{const e=Object(a.useContext)(sr);if(!e)throw new Error("useChat must be used within a ChatProvider");return e};r(612);var mr=e=>{let{content:t,style:r={}}=e;const[n,l]=Object(a.useState)("");return Object(a.useEffect)(()=>{n&&window.hljs&&document.querySelectorAll(".markdown-content pre code").forEach(e=>{window.hljs.highlightElement(e)})},[n]),Object(a.useEffect)(()=>{(()=>{if(t)try{if("undefined"===typeof window.marked)return console.warn("Marked library not found. Falling back to basic rendering."),void l(t);window.marked.setOptions({breaks:!0,gfm:!0,headerIds:!0,mangle:!1,sanitize:!1,smartLists:!0,smartypants:!0,xhtml:!1,highlight:function(e,t){if(window.hljs&&t&&window.hljs.getLanguage(t))try{return window.hljs.highlight(e,{language:t}).value}catch(r){console.error("Error highlighting code:",r)}return e}});const r=window.marked.parse(t);l(r)}catch(e){console.error("Error rendering markdown:",e),l(`<p>${t}</p>`)}else l("")})()},[t]),o.a.createElement("div",{style:{fontSize:"0.875rem",lineHeight:"1.5",...r},dangerouslySetInnerHTML:{__html:n},className:"markdown-content"})},ur=r(691);r(613);var gr=()=>{h();const{conversations:e,loadConversation:t,startNewConversation:r,currentConversationId:n,formatTimestamp:l,fetchConversations:i,deleteConversation:s}=dr(),{user:c}=ie(),[m,u]=Object(a.useState)(1),[p,f]=Object(a.useState)(!1),[y,E]=Object(a.useState)(null),b=(e.reduce((e,t)=>{const r=new Date(t.created_at).toLocaleDateString();return e[r]||(e[r]=[]),e[r].push(t),e},{}),e.slice(0,4*m)),v=b.reduce((e,t)=>{const r=new Date(t.created_at).toLocaleDateString();return e[r]||(e[r]=[]),e[r].push(t),e},{}),x=e.length>4*m,F=null!==document.querySelector(".admin-page");return o.a.createElement("div",{className:`chat-history ${F?"in-admin-page":""}`,style:F?{height:"calc(100vh - 65px)"}:{}},o.a.createElement("div",{className:"chat-history-header"},o.a.createElement("div",{className:"chat-history-title"},o.a.createElement("h3",null,"Chat History")),o.a.createElement("button",{className:"new-chat-button",onClick:r},o.a.createElement(ur.a,{className:"new-chat-icon"}),"New Chat")),o.a.createElement("div",{className:"chat-history-list"},0===Object.keys(v).length?o.a.createElement("div",{className:"no-conversations"},o.a.createElement("p",null,"No previous conversations")):o.a.createElement(o.a.Fragment,null,Object.entries(v).sort((e,t)=>{let[r]=e,[a]=t;return new Date(a)-new Date(r)}).map(e=>{let[r,a]=e;return o.a.createElement("div",{key:r,className:"conversation-group"},o.a.createElement("div",{className:"date-header"},(e=>{const t=new Date(e),r=new Date;if(t.toDateString()===r.toDateString())return"Today";const a=new Date(r);if(a.setDate(r.getDate()-1),t.toDateString()===a.toDateString())return"Yesterday";const o=new Date(r);return o.setDate(r.getDate()-7),t>o?"Last 7 days":t.toLocaleDateString(void 0,{month:"short",day:"numeric",year:t.getFullYear()!==r.getFullYear()?"numeric":void 0})})(r)),a.map(e=>o.a.createElement("div",{key:e.id,className:`conversation-item ${n===e.id?"active":""}`},o.a.createElement("div",{className:"conversation-content",onClick:()=>t(e.id)},o.a.createElement(d.a,{className:"conversation-icon"}),o.a.createElement("div",{className:"conversation-details"},o.a.createElement("div",{className:"conversation-title"},e.title||`Chat ${l(e.created_at)}`),o.a.createElement("div",{className:"conversation-meta"},"admin"===(null===c||void 0===c?void 0:c.role)&&e.username&&o.a.createElement("div",{className:"conversation-username"},o.a.createElement(w.a,{className:"username-icon"}),e.username),o.a.createElement("div",{className:"message-count"},e.message_count," messages")))),y===e.id?o.a.createElement("div",{className:"delete-confirm"},o.a.createElement("button",{className:"delete-yes",onClick:t=>{t.stopPropagation(),s(e.id),E(null)},title:"Confirm delete"},o.a.createElement(de.a,{className:"delete-icon"})),o.a.createElement("button",{className:"delete-no",onClick:e=>{e.stopPropagation(),E(null)},title:"Cancel"},o.a.createElement(g.a,{className:"cancel-icon"}))):o.a.createElement("button",{className:"delete-button",onClick:t=>{t.stopPropagation(),E(e.id)},title:"Delete conversation"},o.a.createElement(de.a,{className:"delete-icon"})))))}),x&&o.a.createElement("div",{className:"load-more-container"},o.a.createElement("button",{className:"load-more-button",onClick:async()=>{f(!0);try{const t=await i(m+1);return u(m+1),setTimeout(()=>{const e=document.querySelector(".chat-history-list");if(e){const t=e.scrollTop;e.scrollTo({top:t+100,behavior:"smooth"})}},100),t}catch(e){return console.error("Error loading more conversations:",e),[]}finally{f(!1)}},disabled:p},p?"Loading...":o.a.createElement(o.a.Fragment,null,"Load More (",e.length-b.length," remaining)",o.a.createElement(Qt.a,{className:"load-more-icon"})))))))};r(323),r(324);var pr=()=>{const[e,t]=Object(a.useState)(""),[r,n]=Object(a.useState)(!1),[l,i]=Object(a.useState)(!1),s=Object(a.useRef)(null),c=Object(a.useRef)(null),{messages:m,loading:u,error:g,initialized:p,sendMessage:h,clearChat:f,initializeChat:y,formatTimestamp:E,startNewConversation:b}=dr();Object(a.useEffect)(()=>{v()},[m]);const v=()=>{s.current&&s.current.scrollIntoView({behavior:"smooth"})},w=async r=>{if(r.preventDefault(),""===e.trim()||u)return;const a=e;t(""),n(!0);try{await h(a)}catch(g){console.error("Error sending message:",g)}finally{n(!1)}};Object(a.useEffect)(()=>{const e=e=>{c.current&&!c.current.contains(e.target)&&i(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);return o.a.createElement("div",{className:"modern-chat-container admin-page",style:{height:"calc(100vh - 65px)"}},o.a.createElement("div",{className:"modern-chat-main"},o.a.createElement("div",{className:"modern-chat-content"},o.a.createElement("div",{className:"modern-chat-header"},o.a.createElement("div",{className:"modern-chat-header-left"},o.a.createElement("div",{className:"modern-ai-avatar"},o.a.createElement("span",null,"AI")),o.a.createElement("div",{className:"modern-ai-info"},o.a.createElement("h3",{className:"modern-ai-name"},"Document AI Assistant"),o.a.createElement("div",{className:"modern-ai-status"},o.a.createElement("div",{className:`modern-status-indicator ${p?"ready":"initializing"}`}),o.a.createElement("p",{className:"modern-status-text"},p?"Ready":"Initializing...")))),o.a.createElement("div",{className:"modern-chat-actions"},o.a.createElement("button",{onClick:b,className:"modern-action-button",title:"Start a new chat"},o.a.createElement(d.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"New Chat")),o.a.createElement("button",{onClick:y,className:"modern-action-button",title:"Reinitialize the chatbot"},o.a.createElement(be.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"Reset AI")),o.a.createElement("button",{onClick:f,className:"modern-action-button",title:"Clear current chat"},o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"Clear")),o.a.createElement("div",{className:"history-dropdown-container",ref:c},o.a.createElement("button",{onClick:()=>i(!l),className:"modern-action-button",title:"Show chat history"},o.a.createElement(d.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"History"),o.a.createElement(Qt.a,{style:{height:"0.75rem",width:"0.75rem",marginLeft:"0.25rem"}})),l&&o.a.createElement("div",{className:"history-dropdown-content"},o.a.createElement(gr,null))))),o.a.createElement("div",{className:"modern-chat-messages"},o.a.createElement("div",{className:"modern-message-list"},m.map(e=>o.a.createElement("div",{key:e.id,className:`modern-message-container ${e.sender}`},o.a.createElement("div",{className:`modern-message-bubble ${e.sender}`},"bot"===e.sender&&"markdown"===e.format?o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"modern-message-content"},o.a.createElement(mr,{content:e.text,style:{whiteSpace:"pre-wrap"}})),e.document_references&&e.document_references.length>0&&o.a.createElement("div",{className:"modern-document-references"},o.a.createElement("div",{className:"modern-references-title"},"Sources:"),o.a.createElement("div",{className:"modern-references-list"},e.document_references.map((e,t)=>o.a.createElement("div",{key:t,className:"modern-reference-item"},o.a.createElement("a",{href:e.url||"#",target:"_blank",rel:"noopener noreferrer",className:"modern-reference-link",onClick:t=>{e.url||(t.preventDefault(),console.log("Document URL not available:",e),alert(`Document URL not available for: ${e.filename}`))}},(e=>{if(!e)return"Unknown Document";const t=e.split(".");return t.length>1&&t.pop(),t.join(".").split(/[_-]/).map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ")})(e.filename)||`Document ${t+1}`,e.page&&o.a.createElement("span",null," (Page ",e.page,")"))))))):o.a.createElement("div",{className:"modern-message-content"},e.text),o.a.createElement("div",{className:"modern-message-timestamp"},E(e.timestamp))))),r&&o.a.createElement("div",{className:"modern-typing-indicator"},o.a.createElement("div",{className:"modern-typing-bubble"},o.a.createElement("div",{className:"modern-typing-dots"},o.a.createElement("div",{className:"modern-typing-dot"}),o.a.createElement("div",{className:"modern-typing-dot"}),o.a.createElement("div",{className:"modern-typing-dot"})))),g&&o.a.createElement("div",{className:"modern-error-container"},o.a.createElement("div",{className:"modern-error-message"},o.a.createElement(Zt.a,{style:{height:"1rem",width:"1rem"}}),g)),o.a.createElement("div",{ref:s}))),o.a.createElement("div",{className:"modern-chat-input-container",style:{padding:"0.4rem 1rem"}},o.a.createElement("form",{onSubmit:w,className:"modern-chat-input-form"},o.a.createElement("textarea",{value:e,onChange:e=>t(e.target.value),disabled:u||!p,className:"modern-chat-input",placeholder:p?"Ask a question about your documents...":"Initializing AI...",rows:"1",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),w(e))},style:{height:e.split("\n").length>3?"auto":"2.2rem",padding:"0.4rem"}}),o.a.createElement("button",{type:"submit",disabled:u||!p||""===e.trim(),className:"modern-chat-send-button",style:{padding:"0.4rem"}},o.a.createElement(er.a,{className:"modern-send-icon"})))))))},hr=r(696),fr=r(697),yr=r(698),Er=r(699),br=r(700),vr=r(701),wr=r(702),xr=r(703),Fr=r(704);const Dr=Object(a.createContext)(),Cr=e=>{let{children:t}=e;const{user:r,isAuthenticated:n}=ie(),[l,i]=Object(a.useState)([]),[s,c]=Object(a.useState)([]),[d,m]=Object(a.useState)(!1),[u,g]=Object(a.useState)(null);Object(a.useEffect)(()=>{n&&(h(),"admin"===(null===r||void 0===r?void 0:r.role)&&p())},[n,r]);const p=async()=>{try{m(!0),g(null);const e=await A();console.log("Users API response:",e);let t=[];return Array.isArray(e)?t=e:e.users&&Array.isArray(e.users)?t=e.users:e.users&&"object"===typeof e.users?t=Object.keys(e.users).map(t=>{const r=e.users[t];return{id:r.id||t,username:r.username||"Unknown",email:r.email||"",role:r.role||"user",groups:r.groups||[]}}):"object"!==typeof e||e.users||(t=Object.keys(e).map(t=>{const r=e[t];return{id:r.id||t,username:r.username||"Unknown",email:r.email||"",role:r.role||"user",groups:r.groups||[]}})),console.log("Transformed users data:",t),i(t),{success:!0,data:t}}catch(u){return console.error("Error fetching users:",u),g("Failed to fetch users: "+(u.message||u)),{success:!1,error:u.message||u}}finally{m(!1)}},h=async()=>{try{m(!0),g(null);const t=await T();console.log("Groups API response:",t);let a=[];Array.isArray(t)?a=t:t.groups&&Array.isArray(t.groups)?a=t.groups:t.groups&&"object"===typeof t.groups?a=Object.keys(t.groups).map(e=>{const r=t.groups[e];return{id:r.id||e,name:r.name||"Unnamed Group",description:r.description||"",members:r.members||[]}}):"object"!==typeof t||t.groups||(a=Object.keys(t).map(e=>{const r=t[e];return{id:r.id||e,name:r.name||"Unnamed Group",description:r.description||"",members:r.members||[]}})),console.log("Transformed groups data:",a),c(a);try{const t=localStorage.getItem("userData");if(t){const e=JSON.parse(t);if(r&&r.id&&e.id===r.id)if("admin"!==r.role&&a.length>0){const t=a.map(e=>e.id);console.log("Updating user groups in localStorage from API response:",t),e.groups=t,localStorage.setItem("userData",JSON.stringify(e)),(r&&!r.groups||0===r.groups.length)&&(r.groups=t)}else!(r.groups&&r.groups.length>0)||e.groups&&e.groups.length||(console.log("Updating user groups in localStorage from context:",r.groups),e.groups=r.groups,localStorage.setItem("userData",JSON.stringify(e)))}}catch(e){console.error("Error updating localStorage with groups:",e)}return{success:!0,data:a}}catch(u){return console.error("Error fetching groups:",u),g("Failed to fetch groups: "+(u.message||u)),{success:!1,error:u.message||u}}finally{m(!1)}},f={users:l,groups:s,loading:d,error:u,fetchUsers:p,fetchGroups:h,createUser:async e=>{try{m(!0),g(null),console.log("Creating user with data:",e);const t={username:e.username,password:e.password,role:e.role||"user"};e.email&&(t.email=e.email),e.groups&&e.groups.length>0&&(t.groups=e.groups);const r=await O(t);return console.log("Create user response:",r),await p(),{success:!0,userId:r.user_id||r.id}}catch(u){return console.error("Error creating user:",u),g("Failed to create user: "+(u.message||u)),{success:!1,error:"string"===typeof u?u:"Failed to create user"}}finally{m(!1)}},updateUser:async(e,t)=>{try{m(!0),g(null),console.log("Updating user with ID:",e,"Data:",t);const r={username:t.username};t.password&&(r.password=t.password),t.email&&(r.email=t.email),t.role&&(r.role=t.role),t.groups&&t.groups.length>0&&(r.groups=t.groups);const a=await N(e,r);return console.log("Update user response:",a),await p(),{success:!0,data:a}}catch(u){return console.error("Error updating user:",u),g("Failed to update user: "+(u.message||u)),{success:!1,error:"string"===typeof u?u:"Failed to update user"}}finally{m(!1)}},deleteUser:async e=>{try{m(!0),g(null),console.log("Deleting user with ID:",e);const t=await z(e);return console.log("Delete user response:",t),await p(),{success:!0,data:t}}catch(u){return console.error("Error deleting user:",u),g("Failed to delete user: "+(u.message||u)),{success:!1,error:"string"===typeof u?u:"Failed to delete user"}}finally{m(!1)}},createGroup:async e=>{try{m(!0),g(null);const t=await $(e);return await h(),{success:!0,groupId:t.group_id}}catch(u){return console.error("Error creating group:",u),g("Failed to create group"),{success:!1,error:"string"===typeof u?u:"Failed to create group"}}finally{m(!1)}},updateGroup:async(e,t)=>{try{m(!0),g(null),console.log("Updating group with ID:",e,"Data:",t);const a={name:t.name,description:t.description||"",members:t.members||[]};try{const t=await I(e,a);return console.log("Update group result:",t),await h(),{success:!0,groupId:e}}catch(r){return console.error("API error updating group:",r),"string"===typeof r&&r.includes("already exists")?(g("A group with this name already exists. Please choose a different name."),{success:!1,error:"A group with this name already exists"}):(g("Failed to update group: "+(r.message||r)),{success:!1,error:"string"===typeof r?r:"Failed to update group"})}}catch(u){return console.error("Error in updateGroup function:",u),g("Failed to update group: "+(u.message||u)),{success:!1,error:"string"===typeof u?u:"Failed to update group"}}finally{m(!1)}},deleteGroup:async e=>{try{return m(!0),g(null),await R(e),await h(),{success:!0}}catch(u){return console.error("Error deleting group:",u),g("Failed to delete group"),{success:!1,error:"string"===typeof u?u:"Failed to delete group"}}finally{m(!1)}},addUserToGroup:async(e,t)=>{try{return m(!0),g(null),await W(e,t),await p(),await h(),{success:!0}}catch(u){return console.error("Error adding user to group:",u),g("Failed to add user to group"),{success:!1,error:"string"===typeof u?u:"Failed to add user to group"}}finally{m(!1)}},removeUserFromGroup:async(e,t)=>{try{return m(!0),g(null),await _(e,t),await p(),await h(),{success:!0}}catch(u){return console.error("Error removing user from group:",u),g("Failed to remove user from group"),{success:!1,error:"string"===typeof u?u:"Failed to remove user from group"}}finally{m(!1)}}};return o.a.createElement(Dr.Provider,{value:f},t)},Sr=()=>{const e=Object(a.useContext)(Dr);if(!e)throw new Error("useUser must be used within a UserProvider");return e},Br=Object(a.createContext)(),kr=e=>{let{children:t}=e;const{user:r,isAuthenticated:n}=ie(),{checkForNewNotifications:l}=ge(),[i,s]=Object(a.useState)([]),[c,d]=Object(a.useState)(!1),[m,u]=Object(a.useState)(null),g=Object(a.useRef)(!1);Object(a.useEffect)(()=>{n&&p()},[n]);const p=async()=>{if(g.current)return console.log("Already fetching documents, skipping duplicate request"),{success:!0,data:i};try{g.current=!0,d(!0),u(null);const e=await J();console.log("Documents API response:",e);let t=[];return e.documents&&Array.isArray(e.documents)?t=e.documents.map(e=>({id:e.id||e.doc_id,document_number:e.document_number||"",filename:e.filename,name:e.filename,size:e.size||"Unknown",type:e.content_type||"Document",uploadDate:e.uploaded_at||(new Date).toISOString().split("T")[0],status:e.processed?"Active":"Processing",groups:e.groups||[],uploadedBy:e.uploaded_by||"",uploaderName:e.uploader_name||"Unknown"})):e.documents&&"object"===typeof e.documents?t=Object.keys(e.documents).map(t=>{const r=e.documents[t];return{id:r.id||r.doc_id||t,document_number:r.document_number||"",filename:r.filename,name:r.filename,size:r.size||"Unknown",type:r.content_type||"Document",uploadDate:r.uploaded_at||(new Date).toISOString().split("T")[0],status:r.processed?"Active":"Processing",groups:r.groups||[],uploadedBy:r.uploaded_by||"",uploaderName:r.uploader_name||"Unknown"}}):Array.isArray(e)?t=e.map(e=>({id:e.id||e.doc_id,document_number:e.document_number||"",filename:e.filename,name:e.filename,size:e.size||"Unknown",type:e.content_type||"Document",uploadDate:e.uploaded_at||(new Date).toISOString().split("T")[0],status:e.processed?"Active":"Processing",groups:e.groups||[],uploadedBy:e.uploaded_by||"",uploaderName:e.uploader_name||"Unknown"})):"object"===typeof e&&(t=Object.keys(e).map(t=>{const r=e[t];return r?{id:r.id||r.doc_id||t,document_number:r.document_number||"",filename:r.filename,name:r.filename,size:r.size||"Unknown",type:r.content_type||"Document",uploadDate:r.uploaded_at||(new Date).toISOString().split("T")[0],status:r.processed?"Active":"Processing",groups:r.groups||[],uploadedBy:r.uploaded_by||"",uploaderName:r.uploader_name||"Unknown"}:null}).filter(Boolean)),console.log("Transformed document data:",t),s(t),{success:!0,data:t}}catch(m){return console.error("Error fetching documents:",m),u("Failed to fetch documents: "+(m.message||m)),{success:!1,error:m.message||m}}finally{d(!1),g.current=!1}},[h,f]=Object(a.useState)([]),[y,E]=Object(a.useState)([]),[b,v]=Object(a.useState)([]),[w,x]=Object(a.useState)(!1),[F,D]=Object(a.useState)(null),C=async()=>{try{x(!0),D(null);const e=await P();e.items&&f(e.items);const t=await U();t.items&&E(t.items);const r=await G();return r.items&&v(r.items),{success:!0,serviceNames:e.items||[],softwareMenus:t.items||[],issueTypes:r.items||[]}}catch(m){return console.error("Error fetching document metadata:",m),D("Failed to fetch document metadata"),{success:!1,error:"Failed to fetch document metadata"}}finally{x(!1)}};Object(a.useEffect)(()=>{n&&C()},[n]);const S={documents:i,loading:c,error:m,fetchDocuments:p,uploadDocument:async e=>{try{d(!0),u(null);const t=await X(e),r=await p();let a="";if(r.success&&r.data&&r.data.length>0){if(t.document_id){const e=r.data.find(e=>e.id===t.document_id);e&&e.document_number&&(a=e.document_number)}if(!a){const e=[...r.data].sort((e,t)=>new Date(t.uploadDate)-new Date(e.uploadDate));e.length>0&&e[0].document_number&&(a=e[0].document_number)}}return setTimeout(()=>{l()},1e3),{success:!0,documentId:t.document_id,documentNumber:a}}catch(m){if(console.error("Error uploading document:",m),m.message&&(m.message.includes("Google API key or service account file must be provided")||m.message.includes("Google API credentials are missing")||m.message.includes("Google API credentials are not properly configured"))){const e="The document has been stored in the system, but cannot be processed for AI search because Google API credentials are not properly configured. You can still view and manage the document, but it will not be available for AI-powered search until the administrator configures Google API credentials.";return u(e),{success:!1,error:e}}let t="Failed to upload document";return m.response&&m.response.data&&m.response.data.detail?t=m.response.data.detail:"string"===typeof m?t=m:m.message&&(t=m.message),u(t),{success:!1,error:t}}finally{d(!1)}},deleteDocument:async e=>{try{d(!0),u(null);try{await Y(e)}catch(t){if(404!==t.status)throw t;console.log("Document not found (already deleted):",e)}return await p(),setTimeout(()=>{l()},1e3),{success:!0}}catch(m){return console.error("Error deleting document:",m),u("Failed to delete document"),{success:!1,error:m.message||("string"===typeof m?m:"Failed to delete document")}}finally{d(!1)}},setDocumentAccess:async(e,t)=>{try{return d(!0),u(null),await V(e,t),await p(),{success:!0}}catch(m){return console.error("Error setting document access:",m),u("Failed to set document access"),{success:!1,error:"string"===typeof m?m:"Failed to set document access"}}finally{d(!1)}},getDocumentUrl:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3600;try{d(!0),u(null);const r=await q(e,t);return{success:!0,url:r.url,filename:r.filename,document_id:r.document_id}}catch(m){return console.error("Error getting document URL:",m),u("Failed to get document URL"),{success:!1,error:"string"===typeof m?m:"Failed to get document URL"}}finally{d(!1)}},serviceNames:h,softwareMenus:y,issueTypes:b,metadataLoading:w,metadataError:F,fetchDocumentMetadata:C,createServiceName:async e=>{try{x(!0),D(null);const t=await L(e);return await C(),{success:!0,data:t}}catch(m){return console.error("Error creating service name:",m),D("Failed to create service name"),{success:!1,error:"Failed to create service name"}}finally{x(!1)}},createSoftwareMenu:async e=>{try{x(!0),D(null);const t=await M(e);return await C(),{success:!0,data:t}}catch(m){return console.error("Error creating software menu:",m),D("Failed to create software menu"),{success:!1,error:"Failed to create software menu"}}finally{x(!1)}},createIssueType:async e=>{try{x(!0),D(null);const t=await H(e);return await C(),{success:!0,data:t}}catch(m){return console.error("Error creating issue type:",m),D("Failed to create issue type"),{success:!1,error:"Failed to create issue type"}}finally{x(!1)}}};return o.a.createElement(Br.Provider,{value:S},t)},Ar=()=>{const e=Object(a.useContext)(Br);if(!e)throw new Error("useDocument must be used within a DocumentProvider");return e};var jr=()=>{const[e,t]=Object(a.useState)("users"),[r,n]=Object(a.useState)(!1),[l,i]=Object(a.useState)(!1),[s,c]=Object(a.useState)(!1),[d,m]=Object(a.useState)(null),[u,g]=Object(a.useState)(null),[p,h]=Object(a.useState)(""),[f,y]=Object(a.useState)(!1),[E,b]=Object(a.useState)(1),v=document.documentElement.classList.contains("dark"),{users:w,groups:x,loading:F,error:D,fetchUsers:C,fetchGroups:S,createUser:B,updateUser:k,deleteUser:A,createGroup:j,updateGroup:O,deleteGroup:N}=Sr(),{documents:z,fetchDocuments:T,uploadDocument:$,deleteDocument:I,getDocumentUrl:R,serviceNames:W,softwareMenus:_,issueTypes:P,fetchDocumentMetadata:L,createServiceName:U,createSoftwareMenu:M,createIssueType:G}=Ar();console.log("API Users:",w),console.log("API Groups:",x),console.log("API Documents:",z),Object(a.useEffect)(()=>{(async()=>{try{console.log("Fetching users and groups..."),y(!0);try{await C()}catch(D){console.error("Error fetching users:",D)}try{await S()}catch(e){console.error("Error fetching groups:",e)}try{await T(),b(1)}catch(t){console.error("Error fetching documents:",t)}console.log("Data fetching completed")}catch(r){console.error("Error loading data:",r)}finally{y(!1)}})();const t=setInterval(()=>{S().catch(e=>console.error("Error refreshing groups:",e)),T().then(()=>{"documents"===e&&b(1)}).catch(e=>console.error("Error refreshing documents:",e)),C().catch(e=>console.error("Error refreshing users:",e))},6e4);return()=>clearInterval(t)},[]);const H=(null===w||void 0===w?void 0:w.map(e=>({id:e.id,username:e.username,name:e.username,email:e.email||"",role:e.role||"user",status:"Active",last_login:e.last_login,groups:e.groups||[]})))||[],J=(null===x||void 0===x?void 0:x.map(e=>{var t;return{id:e.id,name:e.name,description:e.description||"",members:e.members||[],memberCount:(null===(t=e.members)||void 0===t?void 0:t.length)||0}}))||[],X=z||[],Y=H.filter(e=>{var t,r,a;return(null===(t=e.username||"")||void 0===t?void 0:t.toLowerCase().includes(p.toLowerCase()))||(null===(r=e.email||"")||void 0===r?void 0:r.toLowerCase().includes(p.toLowerCase()))||(null===(a=e.role||"")||void 0===a?void 0:a.toLowerCase().includes(p.toLowerCase()))}),V=J.filter(e=>{var t,r;return(null===(t=e.name||"")||void 0===t?void 0:t.toLowerCase().includes(p.toLowerCase()))||(null===(r=e.description||"")||void 0===r?void 0:r.toLowerCase().includes(p.toLowerCase()))}),q=X.filter(e=>{var t,r,a;return(null===(t=e.name||e.filename||"")||void 0===t?void 0:t.toLowerCase().includes(p.toLowerCase()))||(null===(r=e.type||e.content_type||"")||void 0===r?void 0:r.toLowerCase().includes(p.toLowerCase()))||(null===(a=e.status||"")||void 0===a?void 0:a.toLowerCase().includes(p.toLowerCase()))}),K=Math.ceil(q.length/5),Q=5*E,Z=Q-5,ee=q.slice(Z,Q);return o.a.createElement("div",{className:"card",style:{height:"100%",display:"flex",flexDirection:"column",position:"relative",maxWidth:"100%",overflow:"hidden"}},f&&o.a.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3}},o.a.createElement("div",{style:{width:"5rem",height:"5rem",borderRadius:"50%",border:"0.25rem solid rgba(255, 255, 255, 0.3)",borderTopColor:"#fff",animation:"spin 1s linear infinite"}}),o.a.createElement("style",null,"\n              @keyframes spin {\n                to { transform: rotate(360deg); }\n              }\n            ")),o.a.createElement(()=>{const[e,t]=Object(a.useState)(!1);return e?o.a.createElement("div",{style:{position:"fixed",bottom:"10px",right:"10px",zIndex:1e3,width:"400px",maxHeight:"400px",overflowY:"auto",backgroundColor:"#333",color:"white",padding:"10px",borderRadius:"4px",fontSize:"12px"}},o.a.createElement("button",{onClick:()=>t(!1),style:{position:"absolute",top:"5px",right:"5px",backgroundColor:"transparent",color:"white",border:"none"}},"X"),o.a.createElement("h3",null,"Users (",(null===w||void 0===w?void 0:w.length)||0,")"),o.a.createElement("pre",null,JSON.stringify(w,null,2)),o.a.createElement("h3",null,"Groups (",(null===x||void 0===x?void 0:x.length)||0,")"),o.a.createElement("pre",null,JSON.stringify(x,null,2)),o.a.createElement("h3",null,"Documents (",(null===z||void 0===z?void 0:z.length)||0,")"),o.a.createElement("pre",null,JSON.stringify(z,null,2))):o.a.createElement("button",{onClick:()=>t(!0),style:{position:"fixed",bottom:"10px",right:"10px",zIndex:1e3,padding:"5px 10px",backgroundColor:"#333",color:"white",border:"none",borderRadius:"4px"}},"Show Debug")},null),o.a.createElement("div",{style:{marginBottom:"1.5rem",display:"flex",flexDirection:"column",gap:"1rem"}},o.a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap"}},o.a.createElement("h1",{style:{fontSize:"1.5rem",fontWeight:"600"}},"User & Group Management"),o.a.createElement("div",{style:{display:"flex",gap:"0.75rem"}},"users"===e&&o.a.createElement("button",{onClick:()=>n(!0),className:"btn btn-primary",style:{display:"flex",alignItems:"center",gap:"0.5rem",backgroundColor:v?"#2563EB":"#3B82F6",color:"white",padding:"0.5rem 1rem",borderRadius:"0.375rem",cursor:"pointer"}},o.a.createElement(fr.a,{style:{height:"1.25rem",width:"1.25rem"}}),"Add User"),"groups"===e&&o.a.createElement("button",{onClick:()=>i(!0),className:"btn btn-primary",style:{display:"flex",alignItems:"center",gap:"0.5rem",backgroundColor:v?"#059669":"#10B981",color:"white",padding:"0.5rem 1rem",borderRadius:"0.375rem",cursor:"pointer"}},o.a.createElement(yr.a,{style:{height:"1.25rem",width:"1.25rem"}}),"Add Group"),"documents"===e&&o.a.createElement("button",{onClick:()=>c(!0),className:"btn btn-primary",style:{display:"flex",alignItems:"center",gap:"0.5rem",backgroundColor:v?"#7C3AED":"#8B5CF6",color:"white",padding:"0.5rem 1rem",borderRadius:"0.375rem",cursor:"pointer"}},o.a.createElement(Er.a,{style:{height:"1.25rem",width:"1.25rem"}}),"Upload Document")))),o.a.createElement("div",{style:{marginBottom:"1.5rem"}},o.a.createElement("div",{style:{borderBottom:`1px solid ${v?"#374151":"#E5E7EB"}`,display:"flex"}},o.a.createElement("nav",{style:{display:"flex",gap:"2rem"}},o.a.createElement("button",{onClick:()=>{t("users"),b(1)},style:{padding:"1rem 0.25rem",borderBottom:`2px solid ${"users"===e?v?"#3B82F6":"#2563EB":"transparent"}`,fontWeight:"500",fontSize:"0.875rem",color:"users"===e?v?"#3B82F6":"#2563EB":v?"#9CA3AF":"#6B7280",cursor:"pointer",background:"none",borderWidth:0,borderStyle:"none",borderRadius:0,marginBottom:"-1px"}},"Users"),o.a.createElement("button",{onClick:()=>{t("groups"),b(1)},style:{padding:"1rem 0.25rem",borderBottom:`2px solid ${"groups"===e?v?"#3B82F6":"#2563EB":"transparent"}`,fontWeight:"500",fontSize:"0.875rem",color:"groups"===e?v?"#3B82F6":"#2563EB":v?"#9CA3AF":"#6B7280",cursor:"pointer",background:"none",borderWidth:0,borderStyle:"none",borderRadius:0,marginBottom:"-1px"}},"Groups"),o.a.createElement("button",{onClick:()=>{t("documents"),b(1)},style:{padding:"1rem 0.25rem",borderBottom:`2px solid ${"documents"===e?v?"#3B82F6":"#2563EB":"transparent"}`,fontWeight:"500",fontSize:"0.875rem",color:"documents"===e?v?"#3B82F6":"#2563EB":v?"#9CA3AF":"#6B7280",cursor:"pointer",background:"none",borderWidth:0,borderStyle:"none",borderRadius:0,marginBottom:"-1px",display:"flex",alignItems:"center",gap:"0.25rem"}},o.a.createElement(be.a,{style:{height:"1rem",width:"1rem"}}),"Documents")))),o.a.createElement("div",{style:{marginBottom:"1.5rem",maxWidth:"100%",overflow:"hidden"}},o.a.createElement("div",{style:{position:"relative",maxWidth:"100%"}},o.a.createElement("div",{style:{position:"absolute",top:"50%",transform:"translateY(-50%)",left:"0.75rem",pointerEvents:"none"}},o.a.createElement(br.a,{style:{height:"1.25rem",width:"1.25rem",color:v?"#9CA3AF":"#6B7280"}})),o.a.createElement("input",{type:"text",value:p,onChange:e=>{h(e.target.value),b(1)},style:{display:"block",width:"100%",maxWidth:"100%",paddingLeft:"2.5rem",paddingRight:"0.75rem",paddingTop:"0.5rem",paddingBottom:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none",boxSizing:"border-box"},placeholder:`Search ${e}...`}))),o.a.createElement("div",{style:{flex:"1",borderRadius:"0.5rem",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",overflow:"hidden",backgroundColor:v?"#374151":"#FFFFFF"}},F&&o.a.createElement("div",{style:{padding:"1rem",textAlign:"center"}},o.a.createElement("p",{style:{color:v?"#D1D5DB":"#6B7280"}},"Loading data...")),D&&o.a.createElement("div",{style:{padding:"1rem",backgroundColor:v?"#DC262630":"#FEE2E2",color:v?"#FCA5A5":"#DC2626"}},o.a.createElement("p",null,"Error: ",D),o.a.createElement("button",{onClick:()=>{C(),S()},style:{marginTop:"0.5rem",padding:"0.25rem 0.75rem",backgroundColor:v?"#374151":"#F3F4F6",color:v?"#D1D5DB":"#374151",borderRadius:"0.25rem",border:"none",cursor:"pointer"}},"Retry")),F||D||"users"!==e?F||D||"groups"!==e?F||D||"documents"!==e?o.a.createElement("div",{style:{padding:"1rem",textAlign:"center"}},o.a.createElement("p",{style:{color:v?"#D1D5DB":"#6B7280"}},"No content to display")):o.a.createElement("div",{style:{overflowX:"auto"}},o.a.createElement("table",{style:{minWidth:"100%",borderCollapse:"collapse"}},o.a.createElement("thead",{style:{backgroundColor:v?"#4B5563":"#F9FAFB",borderBottom:`1px solid ${v?"#6B7280":"#E5E7EB"}`}},o.a.createElement("tr",null,o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Document #"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Name"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Type"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Size"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Groups"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Uploaded By"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Upload Date"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"right",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Actions"))),o.a.createElement("tbody",{style:{backgroundColor:v?"#374151":"#FFFFFF",borderTop:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},ee.map(e=>o.a.createElement("tr",{key:e.id,style:{borderBottom:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",fontWeight:"500",color:v?"#F9FAFB":"#111827"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#05966930":"#D1FAE5",color:v?"#A7F3D0":"#059669"}},e.document_number||"N/A")),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",fontWeight:"500",color:v?"#F9FAFB":"#111827",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"0.5rem",overflow:"hidden",padding:"0.5rem 0",maxWidth:"250px"}},o.a.createElement("div",{style:{minWidth:"2rem",height:"2rem",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"0.375rem",backgroundColor:"PDF"===e.type?v?"#DC262630":"#FEE2E2":"DOCX"===e.type?v?"#2563EB30":"#DBEAFE":"XLSX"===e.type?v?"#05966930":"#D1FAE5":v?"#7C3AED30":"#EDE9FE",flexShrink:0,marginTop:"0.125rem"}},o.a.createElement(be.a,{style:{height:"1.25rem",width:"1.25rem",color:"PDF"===e.type?v?"#FCA5A5":"#DC2626":"DOCX"===e.type?v?"#93C5FD":"#2563EB":"XLSX"===e.type?v?"#A7F3D0":"#059669":v?"#C4B5FD":"#7C3AED"}})),o.a.createElement("div",{style:{display:"block",width:"100%",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("span",{style:{display:"block",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",lineHeight:"1.25rem"}},e.name||e.filename||"Unnamed Document"),o.a.createElement("span",{style:{display:"block",fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}},e.filename&&e.name!==e.filename?e.filename:"")))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.type),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.size),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.25rem"}},e.groups&&Array.isArray(e.groups)?e.groups.map((e,t)=>o.a.createElement("span",{key:t,style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#1d4ed830":"#dbeafe",color:v?"#bfdbfe":"#1d4ed8"}},e)):o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#4b546330":"#f3f4f6",color:v?"#9ca3af":"#6b7280"}},"No groups"))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#7e22ce30":"#f3e8ff",color:v?"#e9d5ff":"#7e22ce"}},e.uploaderName||"Unknown")),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.uploadDate),o.a.createElement("td",{style:{padding:"1rem 1.5rem",textAlign:"right",fontSize:"0.875rem",fontWeight:"500",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",height:"2rem",gap:"0.5rem"}},o.a.createElement("button",{onClick:async()=>{try{y(!0);const r=await R(e.id);r.success?window.open(r.url,"_blank"):alert(`Failed to get document URL: ${r.error||"Unknown error"}`)}catch(t){console.error("Error getting document URL:",t),alert(`Error getting document URL: ${t.message||t}`)}finally{y(!1)}},style:{background:"none",border:"none",cursor:"pointer",color:v?"#60A5FA":"#2563EB",display:"flex",alignItems:"center",justifyContent:"center",width:"2rem",height:"2rem",borderRadius:"0.375rem",transition:"background-color 0.2s ease"},title:"View Document",onMouseOver:e=>e.currentTarget.style.backgroundColor=v?"rgba(96, 165, 250, 0.1)":"rgba(37, 99, 235, 0.05)",onMouseOut:e=>e.currentTarget.style.backgroundColor="transparent"},o.a.createElement(wr.a,{style:{height:"1.25rem",width:"1.25rem"}})),o.a.createElement("button",{onClick:async()=>{if(window.confirm(`Are you sure you want to delete document "${e.name||e.filename}"? This action cannot be undone. This will remove the document from S3, the database, and all vector stores.`))try{y(!0);const r=await I(e.id);r.success?(await T(),1===ee.length&&E>1&&b(e=>e-1)):alert(`Failed to delete document: ${r.error||"Unknown error"}`)}catch(t){console.error("Error deleting document:",t),alert(`Error deleting document: ${t.message||t}`)}finally{y(!1)}},style:{background:"none",border:"none",cursor:"pointer",color:v?"#F87171":"#DC2626",display:"flex",alignItems:"center",justifyContent:"center",width:"2rem",height:"2rem",borderRadius:"0.375rem",transition:"background-color 0.2s ease"},title:"Delete Document",onMouseOver:e=>e.currentTarget.style.backgroundColor=v?"rgba(248, 113, 113, 0.1)":"rgba(220, 38, 38, 0.05)",onMouseOut:e=>e.currentTarget.style.backgroundColor="transparent"},o.a.createElement(de.a,{style:{height:"1.25rem",width:"1.25rem"}})))))))),q.length>5&&o.a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"1rem 1.5rem",borderTop:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},o.a.createElement("div",{style:{fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},"Showing ",Z+1,"-",Math.min(Q,q.length)," of ",q.length," documents"),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem"}},o.a.createElement("button",{onClick:()=>{b(e=>Math.max(e-1,1))},disabled:1===E,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:1===E?v?"#374151":"#F3F4F6":v?"#4B5563":"#FFFFFF",color:1===E?v?"#6B7280":"#9CA3AF":v?"#D1D5DB":"#374151",border:`1px solid ${v?"#4B5563":"#D1D5DB"}`,cursor:1===E?"not-allowed":"pointer"},"aria-label":"Previous page"},o.a.createElement(xr.a,{style:{height:"1.25rem",width:"1.25rem"}})),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"0.25rem",fontSize:"0.875rem",fontWeight:"500",color:v?"#D1D5DB":"#374151"}},o.a.createElement("span",null,"Page ",E," of ",K)),o.a.createElement("button",{onClick:()=>{b(e=>Math.min(e+1,K))},disabled:E===K,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:E===K?v?"#374151":"#F3F4F6":v?"#4B5563":"#FFFFFF",color:E===K?v?"#6B7280":"#9CA3AF":v?"#D1D5DB":"#374151",border:`1px solid ${v?"#4B5563":"#D1D5DB"}`,cursor:E===K?"not-allowed":"pointer"},"aria-label":"Next page"},o.a.createElement(Fr.a,{style:{height:"1.25rem",width:"1.25rem"}}))))):o.a.createElement("div",{style:{overflowX:"auto"}},o.a.createElement("table",{style:{minWidth:"100%",borderCollapse:"collapse"}},o.a.createElement("thead",{style:{backgroundColor:v?"#4B5563":"#F9FAFB",borderBottom:`1px solid ${v?"#6B7280":"#E5E7EB"}`}},o.a.createElement("tr",null,o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Name"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Description"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Members"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"right",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Actions"))),o.a.createElement("tbody",{style:{backgroundColor:v?"#374151":"#FFFFFF",borderTop:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},V.map(e=>o.a.createElement("tr",{key:e.id,style:{borderBottom:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",fontWeight:"500",color:v?"#F9FAFB":"#111827"}},e.name),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.description),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#1d4ed830":"#dbeafe",color:v?"#bfdbfe":"#1d4ed8"}},e.memberCount||0," users")),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",textAlign:"right",fontSize:"0.875rem",fontWeight:"500"}},o.a.createElement("button",{onClick:()=>{g(e),i(!0)},style:{background:"none",border:"none",cursor:"pointer",color:v?"#60A5FA":"#2563EB",marginRight:"0.75rem"},title:"Edit Group"},o.a.createElement(vr.a,{style:{height:"1.25rem",width:"1.25rem"}})),o.a.createElement("button",{onClick:async()=>{if(window.confirm(`Are you sure you want to delete group "${e.name}"?`))try{const r=await N(e.id);r.success||alert(`Failed to delete group: ${r.error||"Unknown error"}`)}catch(t){console.error("Error deleting group:",t),alert(`Error deleting group: ${t.message||t}`)}},style:{background:"none",border:"none",cursor:"pointer",color:v?"#F87171":"#DC2626"},title:"Delete Group"},o.a.createElement(de.a,{style:{height:"1.25rem",width:"1.25rem"}})))))))):o.a.createElement("div",{style:{overflowX:"auto"}},0===Y.length?o.a.createElement("div",{style:{padding:"2rem",textAlign:"center",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",gap:"1rem"}},o.a.createElement(fr.a,{style:{height:"3rem",width:"3rem",color:v?"#4B5563":"#D1D5DB"}}),o.a.createElement("h3",{style:{fontSize:"1.125rem",fontWeight:"500",color:v?"#F9FAFB":"#111827",margin:0}},"No users found"),o.a.createElement("p",{style:{fontSize:"0.875rem",maxWidth:"24rem",margin:"0 auto 1rem auto"}},p?`No users match the search term "${p}". Try a different search or clear the filter.`:"There are no users in the system yet, or the users API is currently unavailable. You can add a new user using the button above."),o.a.createElement("button",{onClick:()=>{m(null),n(!0)},style:{display:"inline-flex",alignItems:"center",gap:"0.5rem",padding:"0.5rem 1rem",backgroundColor:v?"#3B82F6":"#2563EB",color:"white",borderRadius:"0.375rem",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:"none"}},o.a.createElement(fr.a,{style:{height:"1rem",width:"1rem"}}),"Add New User"))):o.a.createElement("table",{style:{minWidth:"100%",borderCollapse:"collapse"}},o.a.createElement("thead",{style:{backgroundColor:v?"#4B5563":"#F9FAFB",borderBottom:`1px solid ${v?"#6B7280":"#E5E7EB"}`}},o.a.createElement("tr",null,o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Name"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Email"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Role"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Status"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Groups"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Last Login"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"right",fontSize:"0.75rem",fontWeight:"600",color:v?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em"}},"Actions"))),o.a.createElement("tbody",{style:{backgroundColor:v?"#374151":"#FFFFFF",borderTop:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},Y.map(e=>o.a.createElement("tr",{key:e.id,style:{borderBottom:`1px solid ${v?"#4B5563":"#E5E7EB"}`}},o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",fontWeight:"500",color:v?"#F9FAFB":"#111827"}},e.name||e.username||"Unknown"),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.email||"N/A"),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:"admin"===(e.role||"").toLowerCase()?v?"#7e22ce30":"#f3e8ff":v?"#1d4ed830":"#dbeafe",color:"admin"===(e.role||"").toLowerCase()?v?"#e9d5ff":"#7e22ce":v?"#bfdbfe":"#1d4ed8"}},e.role||"User")),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:"Active"===(e.status||"Active")?v?"#05966930":"#d1fae5":v?"#dc262630":"#fee2e2",color:"Active"===(e.status||"Active")?v?"#a7f3d0":"#059669":v?"#fca5a5":"#dc2626"}},e.status||"Active")),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},o.a.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.25rem"}},e.groups&&Array.isArray(e.groups)&&e.groups.length>0?e.groups.map((e,t)=>{const r=x.find(t=>t.id===e);return o.a.createElement("span",{key:t,style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#1d4ed830":"#dbeafe",color:v?"#bfdbfe":"#1d4ed8"}},r?r.name:e)}):o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:v?"#4b546330":"#f3f4f6",color:v?"#9ca3af":"#6b7280"}},"No groups"))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:v?"#D1D5DB":"#6B7280"}},e.last_login||"Never"),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",textAlign:"right",fontSize:"0.875rem",fontWeight:"500"}},o.a.createElement("button",{onClick:()=>{m(e),n(!0)},style:{background:"none",borderWidth:0,borderStyle:"none",cursor:"pointer",color:v?"#60A5FA":"#2563EB",marginRight:"0.75rem"},title:"Edit User"},o.a.createElement(vr.a,{style:{height:"1.25rem",width:"1.25rem"}})),o.a.createElement("button",{onClick:async()=>{if(window.confirm(`Are you sure you want to delete user "${e.username}"?`))try{const r=await A(e.id);r.success||alert(`Failed to delete user: ${r.error||"Unknown error"}`)}catch(t){console.error("Error deleting user:",t),alert(`Error deleting user: ${t.message||t}`)}},style:{background:"none",borderWidth:0,borderStyle:"none",cursor:"pointer",color:v?"#F87171":"#DC2626"},title:"Delete User"},o.a.createElement(de.a,{style:{height:"1.25rem",width:"1.25rem"}}))))))))),o.a.createElement(e=>{let{isOpen:t,onClose:r,editUser:n=null}=e;const[l,i]=Object(a.useState)({username:"",password:"",role:"user",groups:[]}),[s,c]=Object(a.useState)(""),[d,m]=Object(a.useState)(!1);Object(a.useEffect)(()=>{t&&(i(n?{username:n.username||"",password:"",role:n.role||"user",groups:n.groups||[]}:{username:"",password:"",role:"user",groups:[]}),c(""))},[t,n]);const u=e=>{const{name:t,value:r}=e.target;i(e=>({...e,[t]:r}))};return t?o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:50,overflowY:"auto"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"1rem"}},o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",transition:"opacity 0.3s ease-in-out"},"aria-hidden":"true",onClick:r}),o.a.createElement("div",{style:{position:"relative",backgroundColor:v?"#1F2937":"#FFFFFF",borderRadius:"0.5rem",overflow:"hidden",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",width:"100%",maxWidth:"32rem",margin:"2rem auto",textAlign:"left"}},o.a.createElement("div",{style:{padding:"1.5rem"}},o.a.createElement("h3",{style:{fontSize:"1.25rem",fontWeight:"500",marginBottom:"1rem",color:v?"#F9FAFB":"#111827"}},n?"Edit User":"Add New User"),s&&o.a.createElement("div",{style:{padding:"0.75rem",marginBottom:"1rem",backgroundColor:v?"#DC262630":"#FEE2E2",color:v?"#FCA5A5":"#DC2626",borderRadius:"0.375rem"}},s),o.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),c(""),l.username)if(n||l.password)try{m(!0);const e={username:l.username,role:l.role.toLowerCase(),groups:l.groups};let a;l.password&&(e.password=l.password),(a=n?await k(n.id,e):await B(e)).success?r():c(a.error||"Failed to save user")}catch(t){console.error("Error saving user:",t),c("string"===typeof t?t:"An unexpected error occurred")}finally{m(!1)}else c("Password is required for new users");else c("Username is required")},style:{display:"flex",flexDirection:"column",gap:"1rem"}},o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"username",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Username"),o.a.createElement("input",{type:"text",id:"username",name:"username",value:l.username,onChange:u,style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"password",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},n?"Password (Leave blank to keep current)":"Password"),o.a.createElement("input",{type:"password",id:"password",name:"password",value:l.password,onChange:u,style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"role",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Role"),o.a.createElement("select",{id:"role",name:"role",value:l.role,onChange:u,style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}},o.a.createElement("option",{value:"user"},"User"),o.a.createElement("option",{value:"admin"},"Admin"))),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"groups",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Groups"),o.a.createElement("select",{id:"groups",name:"groups",multiple:!0,value:l.groups,onChange:e=>{const t=Array.from(e.target.selectedOptions).map(e=>e.value);i(e=>({...e,groups:t}))},style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none",height:"8rem"}},V.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name)))),o.a.createElement("div",{style:{padding:"1rem 0",display:"flex",justifyContent:"flex-end",gap:"0.75rem",marginTop:"1rem"}},o.a.createElement("button",{type:"submit",disabled:d,style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:d?v?"#4B5563":"#9CA3AF":v?"#2563EB":"#3B82F6",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:d?"not-allowed":"pointer",border:"none"}},d?"Saving...":"Save"),o.a.createElement("button",{type:"button",onClick:r,disabled:d,style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",color:v?"#D1D5DB":"#374151",fontWeight:"500",fontSize:"0.875rem",cursor:d?"not-allowed":"pointer",border:`1px solid ${v?"#4B5563":"#D1D5DB"}`}},"Cancel"))))))):null},{isOpen:r,onClose:()=>{n(!1),m(null)},editUser:d}),o.a.createElement(e=>{let{isOpen:t,onClose:r,editGroup:n=null}=e;const[l,i]=Object(a.useState)({name:"",description:"",members:[]}),[s,c]=Object(a.useState)(""),[d,m]=Object(a.useState)(!1);Object(a.useEffect)(()=>{t&&(i(n?{name:n.name||"",description:n.description||"",members:n.members||[]}:{name:"",description:"",members:[]}),c(""))},[t,n]);const u=e=>{const{name:t,value:r}=e.target;i(e=>({...e,[t]:r}))};return t?o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:50,overflowY:"auto"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"1rem"}},o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",transition:"opacity 0.3s ease-in-out"},"aria-hidden":"true",onClick:r}),o.a.createElement("div",{style:{position:"relative",backgroundColor:v?"#1F2937":"#FFFFFF",borderRadius:"0.5rem",overflow:"hidden",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",width:"100%",maxWidth:"32rem",margin:"2rem auto",textAlign:"left"}},o.a.createElement("div",{style:{padding:"1.5rem"}},o.a.createElement("h3",{style:{fontSize:"1.25rem",fontWeight:"500",marginBottom:"1rem",color:v?"#F9FAFB":"#111827"}},n?"Edit Group":"Add New Group"),s&&o.a.createElement("div",{style:{padding:"0.75rem",marginBottom:"1rem",backgroundColor:v?"#DC262630":"#FEE2E2",color:v?"#FCA5A5":"#DC2626",borderRadius:"0.375rem"}},s),o.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),c(""),l.name)try{m(!0);const e={name:l.name,description:l.description,members:l.members};let a;(a=n?await O(n.id,e):await j(e)).success?r():c(a.error||"Failed to save group")}catch(t){console.error("Error saving group:",t),c("string"===typeof t?t:"An unexpected error occurred")}finally{m(!1)}else c("Group name is required")},style:{display:"flex",flexDirection:"column",gap:"1rem"}},o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"name",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Group Name"),o.a.createElement("input",{type:"text",id:"name",name:"name",value:l.name,onChange:u,style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"description",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Description"),o.a.createElement("textarea",{id:"description",name:"description",value:l.description,onChange:u,rows:"3",style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none",minHeight:"5rem"}})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"members",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Members"),o.a.createElement("select",{id:"members",name:"members",multiple:!0,value:l.members,onChange:e=>{const t=Array.from(e.target.selectedOptions).map(e=>e.value);i(e=>({...e,members:t}))},style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none",height:"8rem"}},Y.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name||e.username)))),o.a.createElement("div",{style:{padding:"1rem 0",display:"flex",justifyContent:"flex-end",gap:"0.75rem",marginTop:"1rem"}},o.a.createElement("button",{type:"submit",disabled:d,style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:d?v?"#4B5563":"#9CA3AF":v?"#059669":"#10B981",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:d?"not-allowed":"pointer",border:"none"}},d?"Saving...":"Save"),o.a.createElement("button",{type:"button",onClick:r,disabled:d,style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",color:v?"#D1D5DB":"#374151",fontWeight:"500",fontSize:"0.875rem",cursor:d?"not-allowed":"pointer",border:`1px solid ${v?"#4B5563":"#D1D5DB"}`}},"Cancel"))))))):null},{isOpen:l,onClose:()=>{i(!1),g(null)},editGroup:u}),o.a.createElement(e=>{let{isOpen:t,onClose:r}=e;const[n,l]=Object(a.useState)(""),[i,s]=Object(a.useState)(null),[c,d]=Object(a.useState)([]),[m,u]=Object(a.useState)(""),[g,p]=Object(a.useState)(""),[h,f]=Object(a.useState)(""),[E,b]=Object(a.useState)(""),[w,x]=Object(a.useState)(""),[F,D]=Object(a.useState)(""),[C,S]=Object(a.useState)(""),B=o.a.useRef(null);Object(a.useEffect)(()=>{t?k():(l(""),s(null),d([]),u(""),p(""),f(""),b(""),x(""),D(""),S(""))},[t]);const k=async()=>{try{await L()}catch(e){console.error("Error loading metadata options:",e)}};return t?o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:50,overflowY:"auto"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"1rem"}},o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",transition:"opacity 0.3s ease-in-out"},"aria-hidden":"true",onClick:r}),o.a.createElement("div",{style:{position:"relative",backgroundColor:v?"#1F2937":"#FFFFFF",borderRadius:"0.5rem",overflow:"hidden",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",width:"100%",maxWidth:"32rem",margin:"2rem auto",textAlign:"left"}},o.a.createElement("div",{style:{padding:"1.5rem"}},o.a.createElement("h3",{style:{fontSize:"1.25rem",fontWeight:"500",marginBottom:"1rem",color:v?"#F9FAFB":"#111827"}},"Upload Document"),C&&o.a.createElement("div",{style:{padding:"0.75rem",marginBottom:"1rem",backgroundColor:v?"#DC262630":"#FEE2E2",color:v?"#FCA5A5":"#DC2626",borderRadius:"0.375rem"}},C),o.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),i)if(n)try{r(),y(!0);const e=new FormData;e.append("file",i),c.length>0&&(e.append("groups",JSON.stringify(c)),c.forEach(t=>{e.append("group_ids",t)})),m&&e.append("service_name",m),g&&e.append("software_menu",g),h&&e.append("issue_type",h);const a=await(async e=>{try{const r=await $(e);return r.success&&await T(),r}catch(t){return console.error("Error uploading document:",t),t.message&&t.message.includes("Google API key or service account file must be provided")?{success:!1,error:"Backend configuration error: Google API credentials are missing. Please contact your administrator to configure Google API credentials."}:{success:!1,error:"string"===typeof t?t:"Failed to upload document"}}})(e);if(!a.success){let e=a.error||"Unknown error";(e.includes("Google API key or service account file must be provided")||e.includes("Google API credentials are missing")||e.includes("Google API credentials are not properly configured"))&&(e="The document has been stored in the system, but cannot be processed for AI search because Google API credentials are not properly configured. You can still view and manage the document, but it will not be available for AI-powered search until the administrator configures Google API credentials."),alert(`Failed to upload document: ${e}`)}}catch(t){console.error("Error uploading document:",t),alert(`Error uploading document: ${t.message||"An unexpected error occurred"}`)}finally{y(!1)}else S("Please enter a document name");else S("Please select a file to upload")},style:{display:"flex",flexDirection:"column",gap:"1rem"}},o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"documentName",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Document Name"),o.a.createElement("input",{type:"text",id:"documentName",value:n,onChange:e=>l(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"documentFile",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Upload File"),o.a.createElement("div",{onClick:()=>{var e;return null===(e=B.current)||void 0===e?void 0:e.click()},onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation();const t=e.dataTransfer.files[0];if(t){if(t.size>10485760)return void S("File size exceeds 10MB limit");if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/csv","text/plain"].includes(t.type))return void S("File type not supported. Please upload PDF, DOCX, XLSX, CSV or TXT files.");s(t),S(""),n||l(t.name)}},onDragEnter:e=>{e.preventDefault(),e.stopPropagation()},onDragLeave:e=>{e.preventDefault(),e.stopPropagation()},style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"2rem",borderWidth:"2px",borderStyle:"dashed",borderColor:v?"#4B5563":"#D1D5DB",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#F9FAFB",cursor:"pointer",opacity:1,position:"relative",overflow:"hidden"}},i?o.a.createElement(o.a.Fragment,null,o.a.createElement(be.a,{style:{height:"2.5rem",width:"2.5rem",color:v?"#60A5FA":"#2563EB",marginBottom:"0.5rem"}}),o.a.createElement("p",{style:{fontSize:"0.875rem",color:v?"#D1D5DB":"#374151",marginBottom:"0.25rem",fontWeight:"500"}},i.name),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"0.5rem"}},o.a.createElement("span",{style:{fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280",backgroundColor:v?"#4B5563":"#E5E7EB",padding:"0.125rem 0.375rem",borderRadius:"0.25rem"}},i.name.split(".").pop().toUpperCase()),o.a.createElement("span",{style:{fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280"}},(i.size/1024/1024).toFixed(2)," MB")),o.a.createElement("p",{style:{fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280"}},"Click to change file")):o.a.createElement(o.a.Fragment,null,o.a.createElement(hr.a,{style:{height:"2.5rem",width:"2.5rem",color:v?"#9CA3AF":"#6B7280",marginBottom:"0.5rem"}}),o.a.createElement("p",{style:{fontSize:"0.875rem",color:v?"#D1D5DB":"#374151",marginBottom:"0.25rem"}},"Drag and drop your file here, or click to browse"),o.a.createElement("p",{style:{fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280"}},"Supports PDF, DOCX, XLSX, CSV, TXT (Max 10MB)")),o.a.createElement("input",{type:"file",id:"documentFile",ref:B,onChange:e=>{const t=e.target.files[0];if(t){if(t.size>10485760)return void S("File size exceeds 10MB limit");if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/csv","text/plain"].includes(t.type))return void S("File type not supported. Please upload PDF, DOCX, XLSX, CSV or TXT files.");s(t),S(""),n||l(t.name)}},accept:".pdf,.docx,.xlsx,.csv,.txt",style:{display:"none"},disabled:!1}))),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"serviceName",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Service Name"),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem"}},o.a.createElement("select",{id:"serviceName",value:m,onChange:e=>u(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}},o.a.createElement("option",{value:""},"Select a service name"),W.map(e=>o.a.createElement("option",{key:e.id,value:e.name},e.name)))),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem",marginTop:"0.5rem"}},o.a.createElement("input",{type:"text",placeholder:"Add new service name",value:E,onChange:e=>b(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}}),o.a.createElement("button",{type:"button",onClick:async()=>{if(E.trim())try{const t=await U(E);t.success&&t.data&&(u(t.data.name),b(""))}catch(e){console.error("Error adding service name:",e)}},style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#2563EB":"#3B82F6",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:"none"}},"Add"))),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"softwareMenu",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Software Menu"),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem"}},o.a.createElement("select",{id:"softwareMenu",value:g,onChange:e=>p(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}},o.a.createElement("option",{value:""},"Select a software menu"),_.map(e=>o.a.createElement("option",{key:e.id,value:e.name},e.name)))),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem",marginTop:"0.5rem"}},o.a.createElement("input",{type:"text",placeholder:"Add new software menu",value:w,onChange:e=>x(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}}),o.a.createElement("button",{type:"button",onClick:async()=>{if(w.trim())try{const t=await M(w);t.success&&t.data&&(p(t.data.name),x(""))}catch(e){console.error("Error adding software menu:",e)}},style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#2563EB":"#3B82F6",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:"none"}},"Add"))),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"issueType",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Issue Type"),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem"}},o.a.createElement("select",{id:"issueType",value:h,onChange:e=>f(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}},o.a.createElement("option",{value:""},"Select an issue type"),P.map(e=>o.a.createElement("option",{key:e.id,value:e.name},e.name)))),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem",marginTop:"0.5rem"}},o.a.createElement("input",{type:"text",placeholder:"Add new issue type",value:F,onChange:e=>D(e.target.value),style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none"}}),o.a.createElement("button",{type:"button",onClick:async()=>{if(F.trim())try{const t=await G(F);t.success&&t.data&&(f(t.data.name),D(""))}catch(e){console.error("Error adding issue type:",e)}},style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#2563EB":"#3B82F6",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:"none"}},"Add"))),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"documentGroups",style:{display:"block",fontSize:"0.875rem",fontWeight:"500",marginBottom:"0.5rem",color:v?"#D1D5DB":"#374151"}},"Assign to Groups"),o.a.createElement("select",{id:"documentGroups",multiple:!0,value:c,onChange:e=>{const t=Array.from(e.target.selectedOptions).map(e=>e.value);d(t)},style:{display:"block",width:"100%",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:v?"#4B5563":"#D1D5DB",color:v?"#F9FAFB":"#111827",outline:"none",height:"8rem"}},V.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name))),o.a.createElement("p",{style:{fontSize:"0.75rem",color:v?"#9CA3AF":"#6B7280",marginTop:"0.25rem"}},"Only users in these groups will have access to this document")),o.a.createElement("div",{style:{padding:"1rem 0",marginTop:"0.5rem",borderTop:`1px solid ${v?"#4B5563":"#E5E7EB"}`,display:"flex",justifyContent:"flex-end",gap:"0.75rem"}},o.a.createElement("button",{type:"submit",style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#2563EB":"#3B82F6",color:"white",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:"none",minWidth:"10rem",transition:"background-color 0.3s ease"}},"Upload Document"),o.a.createElement("button",{type:"button",onClick:r,style:{display:"inline-flex",justifyContent:"center",alignItems:"center",padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:v?"#374151":"#FFFFFF",color:v?"#D1D5DB":"#374151",fontWeight:"500",fontSize:"0.875rem",cursor:"pointer",border:`1px solid ${v?"#4B5563":"#D1D5DB"}`}},"Cancel"))))))):null},{isOpen:s,onClose:()=>c(!1)}))},Or=r(705),Nr=r(706);var zr=()=>{const e=document.documentElement.classList.contains("dark"),[t,r]=Object(a.useState)({clearConversation:!1,saveVectorStore:!1,resetSystem:!1,processDocuments:!1}),[n,l]=Object(a.useState)({type:null,text:""}),i=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;l({type:e,text:t}),setTimeout(()=>{l({type:null,text:""})},r)};return o.a.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"}},o.a.createElement("div",{style:{display:"flex",flexDirection:"column",marginBottom:"0.5rem"},className:"md-flex-row md-items-center md-justify-between"},o.a.createElement("h1",{style:{fontSize:"1.5rem",fontWeight:"600",color:e?"#F9FAFB":"#111827"}},"Admin Settings")),n.text&&o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.5rem",backgroundColor:"success"===n.type?e?"#065F46":"#D1FAE5":e?"#7F1D1D":"#FEE2E2",color:"success"===n.type?e?"#A7F3D0":"#047857":e?"#FECACA":"#B91C1C",marginBottom:"1rem",whiteSpace:"pre-line",maxHeight:"300px",overflowY:"auto"}},n.text),o.a.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr",gap:"1.5rem"}},o.a.createElement("div",{style:{borderRadius:"0.5rem",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",overflow:"hidden",backgroundColor:e?"#374151":"#FFFFFF"}},o.a.createElement("div",{style:{padding:"1rem 1.5rem",borderBottom:`1px solid ${e?"#4B5563":"#E5E7EB"}`}},o.a.createElement("h3",{style:{fontSize:"1.125rem",fontWeight:"500",color:e?"#F9FAFB":"#111827"}},"System Maintenance"),o.a.createElement("p",{style:{marginTop:"0.25rem",fontSize:"0.875rem",color:e?"#9CA3AF":"#6B7280"}},"Manage system data and perform maintenance operations.")),o.a.createElement("div",{style:{padding:"1rem 1.5rem"}},o.a.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr",gap:"1rem"},className:"md-grid-cols-2"},o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.5rem",backgroundColor:e?"#1F2937":"#F9FAFB",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"0.5rem"}},o.a.createElement(Kt.a,{style:{height:"1.25rem",width:"1.25rem",color:e?"#60A5FA":"#3B82F6",marginRight:"0.5rem"}}),o.a.createElement("h4",{style:{fontSize:"0.875rem",fontWeight:"500",color:e?"#F9FAFB":"#111827"}},"Clear Conversation History")),o.a.createElement("p",{style:{fontSize:"0.75rem",color:e?"#9CA3AF":"#6B7280",marginBottom:"1rem"}},"Clear all conversation history without affecting documents or embeddings."),o.a.createElement("button",{onClick:async()=>{if(window.confirm("Are you sure you want to clear all conversation history?")){r({...t,clearConversation:!0});try{const a=await tr();console.log("Conversation cleared:",a),i("success","Conversation history cleared successfully!")}catch(e){console.error("Error clearing conversation:",e),i("error",`Failed to clear conversation: ${e.message||e}`)}finally{r({...t,clearConversation:!1})}}},disabled:t.clearConversation,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"100%",padding:"0.5rem",border:"none",borderRadius:"0.375rem",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)",fontSize:"0.875rem",fontWeight:"500",color:"white",backgroundColor:t.clearConversation?e?"#4B5563":"#9CA3AF":e?"#2563EB":"#3B82F6",cursor:t.clearConversation?"not-allowed":"pointer"}},t.clearConversation?o.a.createElement(o.a.Fragment,null,o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem",animation:"spin 1s linear infinite"}}),"Clearing..."):"Clear Conversations")),o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.5rem",backgroundColor:e?"#1F2937":"#F9FAFB",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"0.5rem"}},o.a.createElement(Or.a,{style:{height:"1.25rem",width:"1.25rem",color:e?"#60A5FA":"#3B82F6",marginRight:"0.5rem"}}),o.a.createElement("h4",{style:{fontSize:"0.875rem",fontWeight:"500",color:e?"#F9FAFB":"#111827"}},"Save Vector Store")),o.a.createElement("p",{style:{fontSize:"0.75rem",color:e?"#9CA3AF":"#6B7280",marginBottom:"1rem"}},"Save the current vector store to disk for persistence."),o.a.createElement("button",{onClick:async()=>{r({...t,saveVectorStore:!0});try{const a=await ar();console.log("Vector store saved:",a),i("success","Vector store saved successfully!")}catch(e){console.error("Error saving vector store:",e),i("error",`Failed to save vector store: ${e.message||e}`)}finally{r({...t,saveVectorStore:!1})}},disabled:t.saveVectorStore,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"100%",padding:"0.5rem",border:"none",borderRadius:"0.375rem",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)",fontSize:"0.875rem",fontWeight:"500",color:"white",backgroundColor:t.saveVectorStore?e?"#4B5563":"#9CA3AF":e?"#2563EB":"#3B82F6",cursor:t.saveVectorStore?"not-allowed":"pointer"}},t.saveVectorStore?o.a.createElement(o.a.Fragment,null,o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem",animation:"spin 1s linear infinite"}}),"Saving..."):"Save Vector Store")),o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.5rem",backgroundColor:e?"#1F2937":"#F9FAFB",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"0.5rem"}},o.a.createElement(be.a,{style:{height:"1.25rem",width:"1.25rem",color:e?"#60A5FA":"#3B82F6",marginRight:"0.5rem"}}),o.a.createElement("h4",{style:{fontSize:"0.875rem",fontWeight:"500",color:e?"#F9FAFB":"#111827"}},"Process All Documents")),o.a.createElement("p",{style:{fontSize:"0.75rem",color:e?"#9CA3AF":"#6B7280",marginBottom:"1rem"}},"Process all documents in the data folder and update embeddings."),o.a.createElement("button",{onClick:async()=>{r({...t,processDocuments:!0});try{const a=await or();console.log("Documents processed:",a),i("success","Documents processed successfully!")}catch(e){console.error("Error processing documents:",e),i("error",`Failed to process documents: ${e.message||e}`)}finally{r({...t,processDocuments:!1})}},disabled:t.processDocuments,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"100%",padding:"0.5rem",border:"none",borderRadius:"0.375rem",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)",fontSize:"0.875rem",fontWeight:"500",color:"white",backgroundColor:t.processDocuments?e?"#4B5563":"#9CA3AF":e?"#2563EB":"#3B82F6",cursor:t.processDocuments?"not-allowed":"pointer"}},t.processDocuments?o.a.createElement(o.a.Fragment,null,o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem",animation:"spin 1s linear infinite"}}),"Processing..."):"Process Documents")),o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.5rem",backgroundColor:e?"#7F1D1D":"#FEE2E2",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"0.5rem"}},o.a.createElement(Nr.a,{style:{height:"1.25rem",width:"1.25rem",color:e?"#FECACA":"#DC2626",marginRight:"0.5rem"}}),o.a.createElement("h4",{style:{fontSize:"0.875rem",fontWeight:"500",color:e?"#FECACA":"#B91C1C"}},"Hard Reset")),o.a.createElement("p",{style:{fontSize:"0.75rem",color:e?"#FECACA":"#B91C1C",marginBottom:"1rem"}},"WARNING: This will delete all documents and embeddings. This action cannot be undone."),o.a.createElement("button",{onClick:async()=>{if(window.confirm("WARNING: This will delete all documents and embeddings. This action cannot be undone. Are you sure?")&&window.confirm("FINAL WARNING: All data will be permanently deleted. Continue?")){r({...t,resetSystem:!0});try{const n=await nr();if(console.log("Hard reset result:",n),"success"===n.status){const e=n.details||{},t=`\n          Documents deleted: ${e.documents_deleted||0}\n          Embeddings deleted: ${e.embeddings_deleted||0}\n          Document access records deleted: ${e.document_access_deleted||0}\n          Chatbot configs deleted: ${e.chatbot_configs_deleted||0}\n          S3 files deleted: ${e.s3_files_deleted||0}\n          Vector stores deleted: ${e.vector_stores_deleted?"Yes":"No"}\n          User data deleted: ${e.user_data_deleted?"Yes":"No"}\n          Data directory cleaned: ${e.data_dir_cleaned?"Yes":"No"}\n        `;i("success",`System reset successfully! All documents and embeddings have been removed.\n\nDetails:\n${t}\n\nThe page will reload in 3 seconds to complete the reset process.`),setTimeout(()=>{window.location.href="/admin/dashboard"},3e3)}else{var e,a;const t=(null===(e=n.details)||void 0===e?void 0:null===(a=e.errors)||void 0===a?void 0:a.join("\n"))||"";i("error",`Reset completed with issues: ${n.message}\n\n${t}`)}}catch(o){console.error("Error resetting system:",o),i("error",`Failed to reset system: ${o.message||o}`)}finally{r({...t,resetSystem:!1})}}},disabled:t.resetSystem,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"100%",padding:"0.5rem",border:"none",borderRadius:"0.375rem",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)",fontSize:"0.875rem",fontWeight:"500",color:"white",backgroundColor:t.resetSystem?e?"#4B5563":"#9CA3AF":e?"#DC2626":"#EF4444",cursor:t.resetSystem?"not-allowed":"pointer"}},t.resetSystem?o.a.createElement(o.a.Fragment,null,o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem",animation:"spin 1s linear infinite"}}),"Resetting..."):o.a.createElement(o.a.Fragment,null,o.a.createElement(de.a,{style:{height:"1rem",width:"1rem",marginRight:"0.5rem"}}),"Reset System"))))))))};r(707);var Tr=()=>{const[e,t]=Object(a.useState)(""),[r,n]=Object(a.useState)(!1),[l,i]=Object(a.useState)(!1),[s,c]=Object(a.useState)(!1),[m,u]=Object(a.useState)(!1),g=Object(a.useRef)(null),p=Object(a.useRef)(null),h=document.documentElement.classList.contains("dark"),{messages:f,loading:y,error:E,initialized:b,conversations:v,currentConversationId:w,sendMessage:x,clearChat:F,initializeChat:D,formatTimestamp:C,loadConversation:S,startNewConversation:B}=dr(),{user:k}=ie();Object(a.useEffect)(()=>{A()},[f]),Object(a.useEffect)(()=>{const e=()=>{window.innerWidth>=1024?i(!0):i(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),Object(a.useEffect)(()=>{const e=e=>{p.current&&!p.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const A=()=>{g.current&&g.current.scrollIntoView({behavior:"smooth"})},j=async r=>{if(r.preventDefault(),""===e.trim()||y)return;const a=e;t(""),n(!0);try{await x(a)}catch(E){console.error("Error sending message:",E)}finally{n(!1)}};return o.a.createElement("div",{className:"modern-chat-container",style:{height:"calc(100vh - 65px)"}},o.a.createElement("div",{className:"modern-chat-main"},o.a.createElement("div",{className:"modern-chat-content"},o.a.createElement("div",{className:"modern-chat-header"},o.a.createElement("div",{className:"modern-chat-header-left"},o.a.createElement("div",{className:"modern-ai-avatar"},o.a.createElement("span",null,"AI")),o.a.createElement("div",{className:"modern-ai-info"},o.a.createElement("h3",{className:"modern-ai-name"},"Document AI Assistant"),o.a.createElement("div",{className:"modern-ai-status"},o.a.createElement("div",{className:`modern-status-indicator ${b?"ready":"initializing"}`}),o.a.createElement("p",{className:"modern-status-text"},b?"Ready":"Initializing...")))),o.a.createElement("div",{className:"modern-chat-actions"},o.a.createElement("button",{onClick:B,className:"modern-action-button",title:"Start a new chat"},o.a.createElement(d.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"New Chat")),o.a.createElement("button",{onClick:F,className:"modern-action-button",title:"Clear current chat"},o.a.createElement(Kt.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"Clear")),o.a.createElement("div",{className:"history-dropdown-container",ref:p},o.a.createElement("button",{onClick:()=>u(!m),className:"modern-action-button",title:"Show chat history"},o.a.createElement(d.a,{style:{height:"1rem",width:"1rem"}}),o.a.createElement("span",null,"History"),o.a.createElement(Qt.a,{style:{height:"0.75rem",width:"0.75rem",marginLeft:"0.25rem"}})),m&&o.a.createElement("div",{className:"history-dropdown-content"},o.a.createElement(gr,null))))),o.a.createElement("div",{className:"modern-chat-messages"},o.a.createElement("div",{className:"modern-message-list"},0===f.length&&b&&o.a.createElement("div",{className:"empty-state"},o.a.createElement("div",null,o.a.createElement("h3",{style:{fontSize:"1.25rem",fontWeight:"500",marginBottom:"0.5rem",color:h?"#F9FAFB":"#111827"}},"Welcome to Document AI Chat"),o.a.createElement("p",{style:{fontSize:"0.875rem"}},"Ask me anything about your documents and I'll help you find the information you need."))),f.map(e=>o.a.createElement("div",{key:e.id,className:`modern-message-container ${e.sender}`},o.a.createElement("div",{className:`modern-message-bubble ${e.sender}`},"bot"===e.sender&&"markdown"===e.format?o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"modern-message-content"},o.a.createElement(mr,{content:e.text,style:{whiteSpace:"pre-wrap"}})),e.document_references&&e.document_references.length>0&&o.a.createElement("div",{className:"modern-document-references"},o.a.createElement("div",{className:"modern-references-title"},"Sources:"),o.a.createElement("div",{className:"modern-references-list"},e.document_references.map((e,t)=>o.a.createElement("div",{key:t,className:"modern-reference-item"},o.a.createElement("a",{href:e.url||"#",target:"_blank",rel:"noopener noreferrer",className:"modern-reference-link",onClick:t=>{e.url||(t.preventDefault(),console.log("Document URL not available:",e),alert(`Document URL not available for: ${e.filename}`))}},e.filename||`Document ${t+1}`,e.page&&o.a.createElement("span",null," (Page ",e.page,")"))))))):o.a.createElement("div",{className:"modern-message-content"},e.text),o.a.createElement("div",{className:"modern-message-timestamp"},C(e.timestamp))))),r&&o.a.createElement("div",{className:"modern-typing-indicator"},o.a.createElement("div",{className:"modern-typing-bubble"},o.a.createElement("div",{className:"modern-typing-dots"},o.a.createElement("div",{className:"modern-typing-dot"}),o.a.createElement("div",{className:"modern-typing-dot"}),o.a.createElement("div",{className:"modern-typing-dot"})))),E&&o.a.createElement("div",{className:"modern-error-container"},o.a.createElement("div",{className:"modern-error-message"},E)),o.a.createElement("div",{ref:g}))),o.a.createElement("div",{className:"modern-chat-input-container"},o.a.createElement("form",{onSubmit:j,className:"modern-chat-input-form"},o.a.createElement("textarea",{value:e,onChange:e=>t(e.target.value),disabled:y||!b,className:"modern-chat-input",placeholder:b?"Ask a question about your documents...":"Initializing AI...",rows:"1",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),j(e))}}),o.a.createElement("button",{type:"submit",disabled:y||!b||""===e.trim(),className:"modern-chat-send-button"},o.a.createElement(er.a,{className:"modern-send-icon"})))))))};var $r=e=>{let{isOpen:t,onClose:r}=e;const[n,l]=Object(a.useState)(""),[i,s]=Object(a.useState)(null),[c,d]=Object(a.useState)([]),[m,u]=Object(a.useState)(""),[p,h]=Object(a.useState)(!1),[f,y]=Object(a.useState)(!1),[E,b]=Object(a.useState)(""),v=Object(a.useRef)(null),{uploadDocument:w,error:x}=Ar(),{groups:F=[],fetchGroups:D}=Sr(),{user:C}=ie();Object(a.useEffect)(()=>{let e=!0,r=null;if(t){console.log("Modal opened, preparing to fetch groups...");try{const e=localStorage.getItem("userData");if(e){const t=JSON.parse(e);t.groups&&t.groups.length>0&&(console.log("Setting user groups from localStorage:",t.groups),d(t.groups))}}catch(Xr){console.error("Error getting groups from localStorage:",Xr)}r=setTimeout(()=>{console.log("Fetching groups after debounce..."),D().then(()=>{if(e&&(console.log("Groups fetched successfully"),C&&"admin"!==C.role&&(!C.groups||0===C.groups.length)))try{const r=localStorage.getItem("lastUserDetailsFetch"),a=Date.now();!r||a-parseInt(r)>6e4?(localStorage.setItem("lastUserDetailsFetch",a.toString()),fetch("http://localhost:8000/users/me/details",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("authToken")}`,"Content-Type":"application/json"}}).then(e=>e.json()).then(t=>{if(e&&t&&t.user&&t.user.groups&&t.user.groups.length>0){console.log("Got user groups directly from /users/me/details:",t.user.groups),d(t.user.groups);try{const e=localStorage.getItem("userData");if(e){const r=JSON.parse(e);r.groups=t.user.groups,localStorage.setItem("userData",JSON.stringify(r))}}catch(Xr){console.error("Error updating localStorage:",Xr)}}}).catch(e=>{console.error("Error fetching user details:",e)})):console.log("Skipping user details fetch - fetched recently")}catch(t){console.error("Error in direct user details fetch:",t)}})},500)}return()=>{e=!1,r&&clearTimeout(r)}},[t,D,C]);const S=document.documentElement.classList.contains("dark");Object(a.useEffect)(()=>{t||(l(""),s(null),d([]),u(""),y(!1),b(""))},[t]),Object(a.useEffect)(()=>{if(t&&C)if(C.groups&&C.groups.length>0)console.log("Setting user groups in modal:",C.groups),d(C.groups);else try{const t=localStorage.getItem("userData");if(t){const e=JSON.parse(t);e.groups&&Array.isArray(e.groups)&&e.groups.length>0&&(console.log("Setting user groups from localStorage:",e.groups),d(e.groups))}}catch(e){console.error("Error getting groups from localStorage:",e)}},[t,C]),Object(a.useEffect)(()=>{t&&(console.log("Modal opened with user:",C),console.log("Available groups:",F),console.log("Selected groups:",c))},[t,C,F,c]),Object(a.useEffect)(()=>{x&&u(x)},[x]);return t?o.a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:50}},o.a.createElement("div",{style:{backgroundColor:S?"#1F2937":"#FFFFFF",borderRadius:"0.5rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",width:"90%",maxWidth:"500px",maxHeight:"90vh",overflow:"auto"}},o.a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"1rem 1.5rem",borderBottom:`1px solid ${S?"#374151":"#E5E7EB"}`}},o.a.createElement("h2",{style:{fontSize:"1.25rem",fontWeight:"600",color:S?"#F9FAFB":"#111827"}},"Upload Document"),o.a.createElement("button",{onClick:r,style:{background:"none",border:"none",cursor:"pointer",color:S?"#9CA3AF":"#6B7280"}},o.a.createElement(g.a,{style:{height:"1.5rem",width:"1.5rem"}}))),o.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),!i)return void u("Please select a file to upload");let t=c;if(0===c.length&&C&&C.groups&&C.groups.length>0)console.log("Using user groups from context:",C.groups),t=C.groups;else if(0===c.length)try{const e=localStorage.getItem("userData");if(e){const r=JSON.parse(e);r.groups&&Array.isArray(r.groups)&&r.groups.length>0&&(console.log("Using user groups from localStorage:",r.groups),t=r.groups)}}catch(a){console.error("Error getting groups from localStorage:",a)}if(0!==t.length||C&&"admin"===C.role)try{h(!0),u("");const e=new FormData;e.append("file",i),t&&t.length>0?(console.log("Adding groups to form data:",t),e.append("groups",JSON.stringify(t)),t.forEach(t=>{e.append("group_ids",t)})):(console.log("No groups selected for document upload"),e.append("groups","[]"));const o=await w(e);o.success?o.documentNumber?(b(o.documentNumber),y(!0)):r():u(o.error||"Failed to upload document")}catch(a){console.error("Error uploading document:",a),u(a.message||"An error occurred while uploading the document")}finally{h(!1)}else F&&F.length>0?u("You are not a member of any groups. Please contact your administrator."):u("No groups are available in the system. Please contact your administrator.")}},o.a.createElement("div",{style:{padding:"1.5rem"}},o.a.createElement("div",{style:{marginBottom:"1.5rem",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"2rem",borderRadius:"0.5rem",border:`2px dashed ${S?"#4B5563":"#D1D5DB"}`,backgroundColor:S?"#374151":"#F9FAFB",cursor:f?"default":"pointer",opacity:f?.7:1},onClick:()=>{var e;return!f&&(null===(e=v.current)||void 0===e?void 0:e.click())}},o.a.createElement("input",{type:"file",ref:v,onChange:e=>{const t=e.target.files[0];t&&(s(t),n||l(t.name))},style:{display:"none"},accept:".pdf,.docx,.doc,.txt",disabled:f}),i?o.a.createElement(o.a.Fragment,null,o.a.createElement(be.a,{style:{height:"2.5rem",width:"2.5rem",color:S?"#60A5FA":"#2563EB",marginBottom:"0.5rem"}}),o.a.createElement("p",{style:{fontSize:"0.875rem",color:S?"#D1D5DB":"#374151",marginBottom:"0.25rem",fontWeight:"500"}},i.name),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"0.5rem"}},o.a.createElement("span",{style:{fontSize:"0.75rem",color:S?"#9CA3AF":"#6B7280",backgroundColor:S?"#4B5563":"#E5E7EB",padding:"0.125rem 0.375rem",borderRadius:"0.25rem"}},i.name.split(".").pop().toUpperCase()),o.a.createElement("span",{style:{fontSize:"0.75rem",color:S?"#9CA3AF":"#6B7280"}},(i.size/1024/1024).toFixed(2)," MB")),!f&&o.a.createElement("button",{type:"button",onClick:e=>{e.stopPropagation(),s(null),l("")},style:{fontSize:"0.75rem",color:S?"#60A5FA":"#2563EB",background:"none",border:"none",cursor:"pointer",padding:"0.25rem 0.5rem",borderRadius:"0.25rem",backgroundColor:S?"#1E40AF20":"#DBEAFE"}},"Change File")):o.a.createElement(o.a.Fragment,null,o.a.createElement(hr.a,{style:{height:"2.5rem",width:"2.5rem",color:S?"#9CA3AF":"#6B7280",marginBottom:"0.5rem"}}),o.a.createElement("p",{style:{fontSize:"0.875rem",color:S?"#D1D5DB":"#374151",marginBottom:"0.25rem",fontWeight:"500"}},"Click to upload a document"),o.a.createElement("p",{style:{fontSize:"0.75rem",color:S?"#9CA3AF":"#6B7280"}},"PDF, DOCX, DOC, TXT up to 10MB"))),C&&"admin"===C.role&&o.a.createElement("div",{style:{marginBottom:"1.5rem"}},o.a.createElement("label",{style:{display:"block",fontSize:"0.875rem",fontWeight:"500",color:S?"#D1D5DB":"#374151",marginBottom:"0.5rem"}},"Select Groups"),o.a.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.5rem",maxHeight:"150px",overflowY:"auto",padding:"0.5rem",border:`1px solid ${S?"#4B5563":"#D1D5DB"}`,borderRadius:"0.375rem"}},F&&F.length>0?F.map(e=>o.a.createElement("div",{key:e.id,style:{display:"flex",alignItems:"center",gap:"0.5rem",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:c.includes(e.id)?S?"#1E40AF20":"#DBEAFE":S?"#374151":"#F3F4F6",cursor:"pointer"},onClick:()=>{c.includes(e.id)?d(c.filter(t=>t!==e.id)):d([...c,e.id])}},o.a.createElement("input",{type:"checkbox",checked:c.includes(e.id),onChange:()=>{},style:{cursor:"pointer"}}),o.a.createElement("span",{style:{fontSize:"0.875rem",color:S?"#D1D5DB":"#374151"}},e.name))):o.a.createElement("p",{style:{fontSize:"0.875rem",color:S?"#9CA3AF":"#6B7280",padding:"0.5rem"}},"No groups available"))),C&&"admin"!==C.role&&o.a.createElement("div",{style:{marginBottom:"1.5rem"}},o.a.createElement("label",{style:{display:"block",fontSize:"0.875rem",fontWeight:"500",color:S?"#D1D5DB":"#374151",marginBottom:"0.5rem"}},"Your Document Will Be Available To:"),C.groups&&C.groups.length>0?o.a.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.5rem",padding:"0.5rem",border:`1px solid ${S?"#4B5563":"#D1D5DB"}`,borderRadius:"0.375rem",backgroundColor:S?"#374151":"#F9FAFB"}},F.length>0?F.filter(e=>C.groups.includes(e.id)).map(e=>o.a.createElement("div",{key:e.id,style:{display:"flex",alignItems:"center",gap:"0.5rem",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:S?"#1E40AF20":"#DBEAFE"}},o.a.createElement("span",{style:{fontSize:"0.875rem",color:S?"#D1D5DB":"#374151"}},e.name))):C.groups.map(e=>o.a.createElement("div",{key:e,style:{display:"flex",alignItems:"center",gap:"0.5rem",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:S?"#1E40AF20":"#DBEAFE"}},o.a.createElement("span",{style:{fontSize:"0.875rem",color:S?"#D1D5DB":"#374151"}},"Group ",e.substring(0,8),"...")))):o.a.createElement("div",{style:{padding:"0.75rem",backgroundColor:S?"rgba(248, 113, 113, 0.1)":"#FEE2E2",color:S?"#F87171":"#DC2626",borderRadius:"0.375rem",fontSize:"0.875rem"}},"You are not a member of any groups. Your administrator will need to assign you to a group.")),m&&o.a.createElement("div",{style:{padding:"0.75rem",marginBottom:"1rem",backgroundColor:S?"rgba(248, 113, 113, 0.1)":"#FEE2E2",color:S?"#F87171":"#DC2626",borderRadius:"0.375rem",fontSize:"0.875rem"}},m),f&&E&&o.a.createElement("div",{style:{padding:"0.75rem",marginBottom:"1rem",backgroundColor:S?"rgba(16, 185, 129, 0.1)":"#D1FAE5",color:S?"#34D399":"#059669",borderRadius:"0.375rem",fontSize:"0.875rem",display:"flex",flexDirection:"column",gap:"0.5rem"}},o.a.createElement("div",null,"Document uploaded successfully!"),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",fontWeight:"600"}},"Document Number:",o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",backgroundColor:S?"rgba(16, 185, 129, 0.2)":"#A7F3D0",borderRadius:"0.25rem",fontFamily:"monospace"}},E))),o.a.createElement("div",{style:{display:"flex",justifyContent:"flex-end",gap:"0.75rem"}},!f&&o.a.createElement("button",{type:"button",onClick:r,style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:S?"#374151":"#F3F4F6",color:S?"#D1D5DB":"#374151",border:"none",fontSize:"0.875rem",fontWeight:"500",cursor:"pointer"}},"Cancel"),f?o.a.createElement("button",{type:"button",onClick:r,style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:S?"#059669":"#10B981",color:"white",border:"none",fontSize:"0.875rem",fontWeight:"500",cursor:"pointer",display:"flex",alignItems:"center",gap:"0.5rem"}},"Close"):o.a.createElement("button",{type:"submit",disabled:p||!i,style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",backgroundColor:p||!i?S?"#4B5563":"#D1D5DB":S?"#7C3AED":"#8B5CF6",color:"white",border:"none",fontSize:"0.875rem",fontWeight:"500",cursor:p||!i?"not-allowed":"pointer",display:"flex",alignItems:"center",gap:"0.5rem"}},p?"Uploading...":"Upload Document")))))):null};var Ir=()=>{const[e,t]=Object(a.useState)(""),[r,n]=Object(a.useState)(!1),[l,i]=Object(a.useState)(!1),[s,c]=Object(a.useState)(1),d=document.documentElement.classList.contains("dark"),{documents:m,fetchDocuments:u,uploadDocument:g,deleteDocument:p,getDocumentUrl:h,loading:f,error:y}=Ar();Object(a.useEffect)(()=>{(async()=>{try{n(!0),await u()}catch(e){console.error("Error fetching documents:",e)}finally{n(!1)}})()},[]);const E=(null===m||void 0===m?void 0:m.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())))||[],b=Math.ceil(E.length/5),v=5*s,w=v-5,x=E.slice(w,v);return o.a.createElement("div",{className:"user-documents-container",style:{height:"calc(100vh - 65px)",display:"flex",flexDirection:"column"}},o.a.createElement("div",{className:"user-documents-header",style:{display:"flex",flexDirection:"column",gap:"1rem",marginBottom:"1rem"}},o.a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"1rem"}},o.a.createElement("h2",{style:{fontSize:"1.5rem",fontWeight:"600",color:d?"#F9FAFB":"#111827"}},"My Documents"),o.a.createElement("button",{onClick:()=>i(!0),style:{display:"flex",alignItems:"center",gap:"0.5rem",backgroundColor:d?"#7C3AED":"#8B5CF6",color:"white",padding:"0.5rem 1rem",borderRadius:"0.375rem",cursor:"pointer",border:"none",whiteSpace:"nowrap"}},o.a.createElement(Er.a,{style:{height:"1.25rem",width:"1.25rem"}}),"Upload Document")),o.a.createElement("div",{style:{position:"relative",maxWidth:"100%",width:"100%",marginBottom:"0.5rem"}},o.a.createElement("div",{style:{position:"relative",width:"100%",maxWidth:"500px"}},o.a.createElement("input",{type:"text",placeholder:"Search documents...",value:e,onChange:e=>{t(e.target.value),c(1)},style:{width:"100%",padding:"0.75rem 1rem 0.75rem 2.75rem",borderRadius:"0.5rem",border:`1px solid ${d?"#4B5563":"#E5E7EB"}`,backgroundColor:d?"#374151":"#F9FAFB",color:d?"#F9FAFB":"#111827",fontSize:"0.875rem",boxSizing:"border-box",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}}),o.a.createElement(br.a,{style:{position:"absolute",left:"1rem",top:"50%",transform:"translateY(-50%)",height:"1.25rem",width:"1.25rem",color:d?"#9CA3AF":"#6B7280"}})))),o.a.createElement("div",{style:{overflowX:"auto",flex:"1 1 auto",display:"flex",flexDirection:"column"}},r||f?o.a.createElement("div",{style:{padding:"2rem",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",gap:"1rem"}},o.a.createElement("div",{style:{width:"2rem",height:"2rem",border:`2px solid ${d?"#4B5563":"#E5E7EB"}`,borderTopColor:d?"#60A5FA":"#3B82F6",borderRadius:"50%",animation:"spin 1s linear infinite"}}),o.a.createElement("p",{style:{color:d?"#D1D5DB":"#6B7280"}},"Loading documents..."),o.a.createElement("style",null,"\n                @keyframes spin {\n                  to { transform: rotate(360deg); }\n                }\n              ")):y?o.a.createElement("div",{style:{padding:"2rem",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",gap:"1rem",color:d?"#F87171":"#DC2626"}},o.a.createElement("p",null,"Error loading documents: ",y),o.a.createElement("button",{onClick:()=>u(),style:{padding:"0.5rem 1rem",backgroundColor:d?"#374151":"#F3F4F6",color:d?"#F9FAFB":"#111827",border:"none",borderRadius:"0.375rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"}},"Try Again")):E.length>0?o.a.createElement("table",{style:{width:"100%",minWidth:"800px",borderCollapse:"separate",borderSpacing:0,tableLayout:"fixed"}},o.a.createElement("thead",{style:{backgroundColor:d?"#4B5563":"#F9FAFB",borderBottom:`1px solid ${d?"#6B7280":"#E5E7EB"}`}},o.a.createElement("tr",null,o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"15%"}},"Document #"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"20%"}},"Name"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"8%"}},"Type"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"10%"}},"Size"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"15%"}},"Groups"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"12%"}},"Uploaded By"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"left",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"12%"}},"Upload Date"),o.a.createElement("th",{style:{padding:"0.75rem 1.5rem",textAlign:"right",fontSize:"0.75rem",fontWeight:"600",color:d?"#D1D5DB":"#6B7280",textTransform:"uppercase",letterSpacing:"0.05em",width:"10%"}},"Actions"))),o.a.createElement("tbody",{style:{backgroundColor:d?"#374151":"#FFFFFF",borderTop:`1px solid ${d?"#4B5563":"#E5E7EB"}`}},x.map(e=>o.a.createElement("tr",{key:e.id,style:{borderBottom:`1px solid ${d?"#4B5563":"#E5E7EB"}`}},o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",fontWeight:"500",color:d?"#F9FAFB":"#111827",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",height:"2rem"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.875rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"0.375rem",backgroundColor:d?"#4B5563":"#F3F4F6",color:d?"#E5E7EB":"#374151"}},e.document_number||"N/A"))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",fontWeight:"500",color:d?"#F9FAFB":"#111827",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"0.5rem",overflow:"hidden",padding:"0.5rem 0"}},o.a.createElement("div",{style:{minWidth:"2rem",height:"2rem",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"0.375rem",backgroundColor:"PDF"===e.type?d?"#DC262630":"#FEE2E2":"DOCX"===e.type?d?"#2563EB30":"#DBEAFE":"XLSX"===e.type?d?"#05966930":"#D1FAE5":d?"#7C3AED30":"#EDE9FE",flexShrink:0,marginTop:"0.125rem"}},o.a.createElement(be.a,{style:{height:"1.25rem",width:"1.25rem",color:"PDF"===e.type?d?"#FCA5A5":"#DC2626":"DOCX"===e.type?d?"#93C5FD":"#2563EB":"XLSX"===e.type?d?"#A7F3D0":"#059669":d?"#C4B5FD":"#7C3AED"}})),o.a.createElement("span",{style:{whiteSpace:"normal",wordBreak:"break-word",lineHeight:"1.25rem"}},e.name))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",height:"2rem"}},e.type)),o.a.createElement("td",{style:{padding:"1rem 1.5rem",whiteSpace:"nowrap",fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",height:"2rem"}},e.size)),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.25rem",minHeight:"2rem",alignItems:"center"}},e.groups&&Array.isArray(e.groups)?e.groups.map((e,t)=>o.a.createElement("span",{key:t,style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:d?"#1d4ed830":"#dbeafe",color:d?"#bfdbfe":"#1d4ed8"}},e)):o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:d?"#4b546330":"#f3f4f6",color:d?"#9ca3af":"#6b7280"}},"No groups"))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",height:"2rem"}},o.a.createElement("span",{style:{padding:"0.125rem 0.5rem",display:"inline-flex",fontSize:"0.75rem",lineHeight:"1.25rem",fontWeight:"600",borderRadius:"9999px",backgroundColor:d?"#7e22ce30":"#f3e8ff",color:d?"#e9d5ff":"#7e22ce",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis"}},e.uploaderName||"Unknown"))),o.a.createElement("td",{style:{padding:"1rem 1.5rem",fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",height:"2rem"}},e.uploadDate)),o.a.createElement("td",{style:{padding:"1rem 1.5rem",textAlign:"right",fontSize:"0.875rem",fontWeight:"500",overflow:"hidden",textOverflow:"ellipsis"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",height:"2rem"}},o.a.createElement("button",{onClick:async()=>{try{n(!0);const r=await h(e.id);r.success?window.open(r.url,"_blank"):alert(`Failed to get document URL: ${r.error||"Unknown error"}`)}catch(t){console.error("Error getting document URL:",t),alert(`Error getting document URL: ${t.message||t}`)}finally{n(!1)}},style:{background:"none",border:"none",cursor:"pointer",color:d?"#60A5FA":"#2563EB",display:"flex",alignItems:"center",justifyContent:"center",width:"2rem",height:"2rem",borderRadius:"0.375rem",transition:"background-color 0.2s ease"},title:"View Document",onMouseOver:e=>e.currentTarget.style.backgroundColor=d?"rgba(96, 165, 250, 0.1)":"rgba(37, 99, 235, 0.05)",onMouseOut:e=>e.currentTarget.style.backgroundColor="transparent"},o.a.createElement(wr.a,{style:{height:"1.25rem",width:"1.25rem"}})))))))):o.a.createElement("div",{style:{padding:"1rem",textAlign:"center"}},o.a.createElement("p",{style:{color:d?"#D1D5DB":"#6B7280"}},"No documents found"))),E.length>5&&o.a.createElement("div",{style:{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",padding:"1rem 1.5rem",borderTop:`1px solid ${d?"#4B5563":"#E5E7EB"}`,flexWrap:"wrap",gap:"1rem",marginTop:"auto"}},o.a.createElement("div",{style:{fontSize:"0.875rem",color:d?"#D1D5DB":"#6B7280",whiteSpace:"nowrap"}},"Showing ",w+1,"-",Math.min(v,E.length)," of ",E.length," documents"),o.a.createElement("div",{style:{display:"flex",gap:"0.5rem",marginLeft:"auto"}},o.a.createElement("button",{onClick:()=>{c(e=>Math.max(e-1,1))},disabled:1===s,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:1===s?d?"#374151":"#F3F4F6":d?"#4B5563":"#FFFFFF",color:1===s?d?"#6B7280":"#9CA3AF":d?"#D1D5DB":"#374151",border:`1px solid ${d?"#4B5563":"#D1D5DB"}`,cursor:1===s?"not-allowed":"pointer"},"aria-label":"Previous page"},o.a.createElement(xr.a,{style:{height:"1.25rem",width:"1.25rem"}})),o.a.createElement("div",{style:{display:"flex",alignItems:"center",gap:"0.25rem",fontSize:"0.875rem",fontWeight:"500",color:d?"#D1D5DB":"#374151",minWidth:"100px",justifyContent:"center"}},o.a.createElement("span",null,"Page ",s," of ",b)),o.a.createElement("button",{onClick:()=>{c(e=>Math.min(e+1,b))},disabled:s===b,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.5rem",borderRadius:"0.375rem",backgroundColor:s===b?d?"#374151":"#F3F4F6":d?"#4B5563":"#FFFFFF",color:s===b?d?"#6B7280":"#9CA3AF":d?"#D1D5DB":"#374151",border:`1px solid ${d?"#4B5563":"#D1D5DB"}`,cursor:s===b?"not-allowed":"pointer"},"aria-label":"Next page"},o.a.createElement(Fr.a,{style:{height:"1.25rem",width:"1.25rem"}})))),o.a.createElement($r,{isOpen:l,onClose:()=>i(!1)}))};var Rr=e=>{let{message:t,apiUrl:r,onRetry:n,onDismiss:l}=e;const[i,s]=Object(a.useState)(!1),[c,d]=Object(a.useState)(30);return Object(a.useEffect)(()=>{if(c>0){const e=setTimeout(()=>d(c-1),1e3);return()=>clearTimeout(e)}0===c&&n&&(n(),d(30))},[c,n]),o.a.createElement("div",{style:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",width:"90%",maxWidth:"600px",backgroundColor:"#FEF2F2",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",padding:"16px",zIndex:1e3,border:"1px solid #FCA5A5"}},o.a.createElement("div",{style:{display:"flex",alignItems:"flex-start",justifyContent:"space-between"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center"}},o.a.createElement("div",{style:{backgroundColor:"#FEE2E2",borderRadius:"50%",padding:"8px",marginRight:"12px"}},o.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"#DC2626",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.a.createElement("circle",{cx:"12",cy:"12",r:"10"}),o.a.createElement("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),o.a.createElement("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"}))),o.a.createElement("div",null,o.a.createElement("h3",{style:{margin:"0 0 4px 0",fontSize:"16px",fontWeight:"600",color:"#991B1B"}},"API Connection Error"),o.a.createElement("p",{style:{margin:"0",fontSize:"14px",color:"#7F1D1D"}},t||"Unable to connect to the API server"))),o.a.createElement("button",{onClick:l,style:{background:"none",border:"none",cursor:"pointer",fontSize:"20px",color:"#9CA3AF",padding:"0"}},"\xd7")),o.a.createElement("div",{style:{marginTop:"12px"}},o.a.createElement("button",{onClick:()=>s(!i),style:{background:"none",border:"none",cursor:"pointer",fontSize:"14px",color:"#991B1B",padding:"0",textDecoration:"underline",marginRight:"16px"}},i?"Hide troubleshooting":"Show troubleshooting"),o.a.createElement("button",{onClick:n,style:{backgroundColor:"#DC2626",color:"white",border:"none",borderRadius:"4px",padding:"6px 12px",fontSize:"14px",cursor:"pointer"}},"Retry now (",c,"s)")),i&&o.a.createElement("div",{style:{marginTop:"16px",padding:"12px",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",color:"#4B5563"}},o.a.createElement("h4",{style:{margin:"0 0 8px 0",fontSize:"14px",fontWeight:"600"}},"Troubleshooting steps:"),o.a.createElement("ul",{style:{margin:"0 0 12px 0",paddingLeft:"20px"}},o.a.createElement("li",null,"Check if the API server is running"),o.a.createElement("li",null,"Verify the API URL: ",o.a.createElement("code",{style:{backgroundColor:"#F3F4F6",padding:"2px 4px",borderRadius:"2px"}},r)),o.a.createElement("li",null,"Check for CORS issues in the browser console"),o.a.createElement("li",null,"Ensure your network connection is stable"),o.a.createElement("li",null,"Try refreshing the page")),o.a.createElement("p",{style:{margin:"0",fontSize:"12px"}},"If the problem persists, please contact your system administrator.")))};var Wr=()=>{const[e,t]=Object(a.useState)(""),[r,n]=Object(a.useState)(""),[l,s]=Object(a.useState)(""),[c,d]=Object(a.useState)(""),[m,u]=Object(a.useState)(!1),[g,p]=Object(a.useState)(!1),[h,f]=Object(a.useState)(!0),[y,E]=Object(a.useState)(!1),{login:b,register:v,isAuthenticated:w,user:x}=ie(),D=Object(i.r)(),C=Object(i.p)(),S=()=>{var e,t,r;if(null===(e=C.state)||void 0===e?void 0:null===(t=e.from)||void 0===t?void 0:t.pathname)return console.log("Login - Redirecting to saved location:",C.state.from.pathname),C.state.from.pathname;const a="admin"===(null===x||void 0===x?void 0:null===(r=x.role)||void 0===r?void 0:r.toLowerCase())?"/admin/dashboard":"/chat";return console.log("Login - Redirecting to default path based on role:",a),a};Object(a.useEffect)(()=>{window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches&&p(!0);const e=localStorage.getItem("darkMode");e&&p("true"===e)},[]),Object(a.useEffect)(()=>{if(w&&x){console.log("Login - Already authenticated, redirecting");const e=S();console.log("Login - Redirect path:",e),D(e)}},[w,D,x,C.state]);return o.a.createElement("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:g?"#111827":"#F3F4F6"}},y&&o.a.createElement(Rr,{message:"Cannot connect to the server. Please check your connection or try again later.",apiUrl:F,onRetry:()=>{E(!1),d("")},onDismiss:()=>{E(!1)}}),o.a.createElement("div",{style:{maxWidth:"28rem",width:"100%",padding:"2.5rem",borderRadius:"0.75rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",backgroundColor:g?"#1F2937":"#FFFFFF"}},o.a.createElement("div",null,o.a.createElement("h2",{style:{marginTop:"1.5rem",textAlign:"center",fontSize:"1.875rem",fontWeight:"800",color:g?"#F9FAFB":"#111827"}},h?"Sign in to your account":"Create a new account"),o.a.createElement("p",{style:{marginTop:"0.5rem",textAlign:"center",fontSize:"0.875rem",color:g?"#9CA3AF":"#6B7280"}},h?"Access the RAG Chatbot dashboard":"Join the RAG Chatbot platform")),o.a.createElement("form",{style:{marginTop:"2rem",display:"flex",flexDirection:"column",gap:"1.5rem"},onSubmit:async t=>{if(t.preventDefault(),e&&r)try{if(u(!0),d(""),h){const t=await b(e,r);if(t.success){console.log("Login response:",t),console.log("Login successful, redirecting");const e=S();console.log("Redirect path after login:",e),D(e)}else d(t.error||"Failed to login")}else{if(r!==l)return d("Passwords do not match"),void u(!1);const t=await v(e,r);t.success?D("/chat"):d(t.error||"Failed to register")}}catch(a){console.error("Login/Register error:",a),a.message&&(a.message.includes("Failed to fetch")||a.message.includes("Network Error")||a.message.includes("Server returned HTML instead of JSON"))?(E(!0),d("Cannot connect to the server. Please check your connection.")):d(a.message||"An unexpected error occurred")}finally{u(!1)}else d("Please enter both username and password")}},c&&o.a.createElement("div",{style:{padding:"1rem",borderRadius:"0.375rem",backgroundColor:"#FEE2E2",color:"#B91C1C"}},c),o.a.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"1rem"}},o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"username",style:{display:"block",marginBottom:"0.5rem",fontSize:"0.875rem",fontWeight:"500",color:g?"#D1D5DB":"#374151"}},"Username"),o.a.createElement("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,value:e,onChange:e=>t(e.target.value),style:{display:"block",width:"100%",padding:"0.625rem",borderRadius:"0.375rem",backgroundColor:g?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:g?"#4B5563":"#D1D5DB",color:g?"#F9FAFB":"#111827",outline:"none"},placeholder:"Enter your username"})),o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"password",style:{display:"block",marginBottom:"0.5rem",fontSize:"0.875rem",fontWeight:"500",color:g?"#D1D5DB":"#374151"}},"Password"),o.a.createElement("input",{id:"password",name:"password",type:"password",autoComplete:h?"current-password":"new-password",required:!0,value:r,onChange:e=>n(e.target.value),style:{display:"block",width:"100%",padding:"0.625rem",borderRadius:"0.375rem",backgroundColor:g?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:g?"#4B5563":"#D1D5DB",color:g?"#F9FAFB":"#111827",outline:"none"},placeholder:h?"Enter your password":"Create a password"})),!h&&o.a.createElement("div",null,o.a.createElement("label",{htmlFor:"confirmPassword",style:{display:"block",marginBottom:"0.5rem",fontSize:"0.875rem",fontWeight:"500",color:g?"#D1D5DB":"#374151"}},"Confirm Password"),o.a.createElement("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:l,onChange:e=>s(e.target.value),style:{display:"block",width:"100%",padding:"0.625rem",borderRadius:"0.375rem",backgroundColor:g?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:g?"#4B5563":"#D1D5DB",color:g?"#F9FAFB":"#111827",outline:"none"},placeholder:"Confirm your password"}))),h&&o.a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},o.a.createElement("div",{style:{display:"flex",alignItems:"center"}},o.a.createElement("input",{id:"remember-me",name:"remember-me",type:"checkbox",style:{height:"1rem",width:"1rem",borderRadius:"0.25rem",backgroundColor:g?"#374151":"#FFFFFF",borderWidth:"1px",borderStyle:"solid",borderColor:g?"#4B5563":"#D1D5DB"}}),o.a.createElement("label",{htmlFor:"remember-me",style:{marginLeft:"0.5rem",fontSize:"0.875rem",color:g?"#D1D5DB":"#374151"}},"Remember me")),o.a.createElement("div",null,o.a.createElement("button",{type:"button",style:{fontSize:"0.875rem",fontWeight:"500",color:g?"#60A5FA":"#2563EB",backgroundColor:"transparent",border:"none",cursor:"pointer"}},"Forgot password?"))),o.a.createElement("div",null,o.a.createElement("button",{type:"submit",disabled:m,style:{display:"flex",width:"100%",justifyContent:"center",alignItems:"center",padding:"0.625rem 1.25rem",borderRadius:"0.375rem",backgroundColor:g?"#2563EB":"#3B82F6",color:"#FFFFFF",fontWeight:"500",fontSize:"0.875rem",border:"none",cursor:m?"not-allowed":"pointer",opacity:m?.7:1}},m?h?"Signing in...":"Creating account...":h?"Sign in":"Create account")),o.a.createElement("div",{style:{marginTop:"1rem",textAlign:"center",fontSize:"0.875rem",color:g?"#9CA3AF":"#6B7280"}},h?"Don't have an account?":"Already have an account?"," ",o.a.createElement("button",{type:"button",onClick:()=>{f(!h),d("")},style:{fontWeight:"500",color:g?"#60A5FA":"#2563EB",backgroundColor:"transparent",border:"none",cursor:"pointer"}},h?"Sign up":"Sign in")),h&&o.a.createElement("div",{style:{marginTop:"1rem",textAlign:"center",fontSize:"0.75rem",color:g?"#6B7280":"#9CA3AF"}},o.a.createElement("p",null,"Demo credentials: admin / password")))))};var _r=()=>{const[e,t]=Object(a.useState)(!1),[r,o]=Object(a.useState)(0),{isAuthenticated:n}=ie();return Object(a.useEffect)(()=>{(async()=>{if(!e){console.log("Checking session on page load...");try{localStorage.getItem("authToken")?!1!==(await re()).success?console.log("Session refreshed successfully on page load"):console.log("Session refresh failed, but continuing without error"):console.log("No token found, skipping session refresh")}catch(r){console.log("Error refreshing session on page load, but continuing:",r)}t(!0)}})()},[e]),Object(a.useEffect)(()=>{if(!n)return;const e=setInterval(async()=>{const e=Date.now();if(e-r<3e4)console.log("Skipping refresh, too soon since last attempt");else{o(e),console.log("Performing token refresh check...");try{!1!==(await re()).success?console.log("Successfully refreshed token"):console.log("Token refresh failed, but continuing without error")}catch(t){console.log("Error during token refresh, but continuing:",t)}}},3e5);return()=>clearInterval(e)},[r,n]),null};const Pr=()=>{try{const t=localStorage.getItem("userData");if(t){const e=JSON.parse(t);return console.log("User data from localStorage:",e),console.log("User role:",e.role),console.log('Is role exactly "admin"?',"admin"===e.role),console.log('Is role exactly "Admin"?',"Admin"===e.role),console.log('Is role "admin" (case-insensitive)?',"admin"===e.role.toLowerCase()),e}return console.log("No user data found in localStorage"),null}catch(e){return console.error("Error debugging user role:",e),null}},Lr=()=>o.a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100vh",flexDirection:"column",backgroundColor:"#f9fafb"}},o.a.createElement("div",{style:{width:"50px",height:"50px",border:"5px solid #e5e7eb",borderTopColor:"#3b82f6",borderRadius:"50%",animation:"spin 1s linear infinite",marginBottom:"1rem"}}),o.a.createElement("style",null,"\n        @keyframes spin {\n          to { transform: rotate(360deg); }\n        }\n      "),o.a.createElement("p",{style:{color:"#6b7280",fontSize:"1rem"}},"Loading application...")),Ur=e=>{var t,r;let{children:a}=e;const{isAuthenticated:n,loading:l,user:s}=ie(),c=Object(i.p)();if(console.log("ProtectedRoute - isAuthenticated:",n),console.log("ProtectedRoute - loading:",l),console.log("ProtectedRoute - user:",s),console.log("ProtectedRoute - location:",c.pathname),l)return o.a.createElement(Lr,null);if(!n||!s)return console.log("Not authenticated, redirecting to login with state:",{from:c}),o.a.createElement(i.a,{to:"/login",state:{from:c},replace:!0});const d=c.pathname.startsWith("/admin"),m=c.pathname.startsWith("/chat");return d&&"admin"!==(null===(t=s.role)||void 0===t?void 0:t.toLowerCase())?(console.log("Non-admin user trying to access admin route, redirecting to chat"),o.a.createElement(i.a,{to:"/chat",replace:!0})):(m&&(null===(r=s.role)||void 0===r||r.toLowerCase()),a)},Mr=()=>{var e,t;const{user:r,loading:a}=ie(),n=Object(i.p)();if(a)return o.a.createElement(Lr,null);console.log("RoleBasedRoute - User from context:",r),console.log("RoleBasedRoute - Current location:",n),Pr();const l=n.pathname,s=l.startsWith("/admin/"),c="/chat"===l||l.startsWith("/chat/");return"admin"===(null===r||void 0===r?void 0:null===(e=r.role)||void 0===e?void 0:e.toLowerCase())&&s?(console.log("Already on admin path, staying here"),null):r&&c?(console.log("Already on chat path, staying here"),null):"admin"===(null===r||void 0===r?void 0:null===(t=r.role)||void 0===t?void 0:t.toLowerCase())?(console.log("Redirecting to admin dashboard"),o.a.createElement(i.a,{to:"/admin/dashboard",replace:!0})):r?(console.log("Redirecting to chat"),o.a.createElement(i.a,{to:"/chat",replace:!0})):(console.log("No user, redirecting to login"),o.a.createElement(i.a,{to:"/login",state:{from:n},replace:!0}))},Gr=()=>{const{user:e,loading:t}=ie(),r=Object(i.p)();return console.log("AdminRoutes - User from context:",e),console.log("AdminRoutes - Current location:",r),Pr(),t?o.a.createElement(Lr,null):e?"admin"!==e.role.toLowerCase()?(console.log("Not an admin, redirecting to chat"),o.a.createElement(i.a,{to:"/chat",replace:!0})):(console.log("User is admin, showing admin dashboard"),o.a.createElement(Ur,null,o.a.createElement(Cr,null,o.a.createElement(kr,null,o.a.createElement(cr,null,o.a.createElement(i.e,null,o.a.createElement(i.c,{path:"/",element:o.a.createElement(ye,null)},o.a.createElement(i.c,{index:!0,element:o.a.createElement(qt,null)}),o.a.createElement(i.c,{path:"dashboard",element:o.a.createElement(qt,null)}),o.a.createElement(i.c,{path:"users",element:o.a.createElement(jr,null)}),o.a.createElement(i.c,{path:"chat",element:o.a.createElement(pr,null)}),o.a.createElement(i.c,{path:"settings",element:o.a.createElement(zr,null)}),o.a.createElement(i.c,{path:"*",element:o.a.createElement(i.a,{to:"/admin/dashboard",replace:!0})})))))))):(console.log("No user, redirecting to login"),o.a.createElement(i.a,{to:"/login",state:{from:r},replace:!0}))},Hr=()=>{var e;const{user:t,loading:r}=ie(),a=Object(i.p)();return console.log("UserRoutes - User from context:",t),console.log("UserRoutes - Current location:",a),r?o.a.createElement(Lr,null):t?"admin"===(null===(e=t.role)||void 0===e?void 0:e.toLowerCase())?(console.log("Admin user in user routes, redirecting to admin dashboard"),o.a.createElement(i.a,{to:"/admin/dashboard",replace:!0})):(console.log("User is regular user, showing chat interface"),o.a.createElement(Ur,null,o.a.createElement(Cr,null,o.a.createElement(kr,null,o.a.createElement(cr,null,o.a.createElement(i.e,null,o.a.createElement(i.c,{path:"/",element:o.a.createElement(ve,null)},o.a.createElement(i.c,{index:!0,element:o.a.createElement(Tr,null)}),o.a.createElement(i.c,{path:"documents",element:o.a.createElement(Ir,null)}),o.a.createElement(i.c,{path:"*",element:o.a.createElement(i.a,{to:"/chat",replace:!0})})))))))):(console.log("No user, redirecting to login"),o.a.createElement(i.a,{to:"/login",state:{from:a},replace:!0}))};var Jr=()=>o.a.createElement(s.a,null,o.a.createElement(f,null,o.a.createElement(le,null,o.a.createElement(pe,null,o.a.createElement(_r,null),o.a.createElement(i.e,null,o.a.createElement(i.c,{path:"/login",element:o.a.createElement(Wr,null)}),o.a.createElement(i.c,{path:"/admin/*",element:o.a.createElement(Gr,null)}),o.a.createElement(i.c,{path:"/chat/*",element:o.a.createElement(Hr,null)}),o.a.createElement(i.c,{path:"/",element:o.a.createElement(Mr,null)}),o.a.createElement(i.c,{path:"*",element:o.a.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh"}},o.a.createElement("h1",{style:{fontSize:"2rem",fontWeight:"bold",marginBottom:"1rem"}},"404"),o.a.createElement("p",{style:{fontSize:"1.25rem",marginBottom:"2rem"}},"Page not found"),o.a.createElement("a",{href:"/",style:{padding:"0.5rem 1rem",backgroundColor:"#3b82f6",color:"white",borderRadius:"0.375rem",textDecoration:"none"}},"Go Home"))}))))));l.a.createRoot(document.getElementById("root")).render(o.a.createElement(o.a.StrictMode,null,o.a.createElement(Jr,null)))}},[[379,1,2]]]);
//# sourceMappingURL=main.a30baa27.chunk.js.map