/* Dark mode fix */
body.dark {
  background-color: #1F2937 !important;
  color: #F9FAFB !important;
}

body.dark .app-container {
  background-color: #1F2937 !important;
}

body.dark .main-content {
  background-color: #1F2937 !important;
}

body.dark .sidebar {
  background-color: #111827 !important;
  color: #F9FAFB !important;
  border-right-color: #374151 !important;
}

body.dark .topbar {
  background-color: #111827 !important;
  border-bottom-color: #374151 !important;
  height: 50px !important; /* Ensure consistent height in dark mode */
}

body.dark .MuiPaper-root {
  background-color: #374151 !important;
  color: #F9FAFB !important;
}

body.dark .MuiTab-root {
  color: #D1D5DB !important;
}

body.dark .MuiTab-root.Mui-selected {
  color: #60A5FA !important;
}

body.dark .MuiTabs-indicator {
  background-color: #60A5FA !important;
}

/* Force dark mode for testing */
.force-dark-mode {
  background-color: #1F2937 !important;
  color: #F9FAFB !important;
}

.force-dark-mode .app-container {
  background-color: #1F2937 !important;
}

.force-dark-mode .main-content {
  background-color: #1F2937 !important;
}

.force-dark-mode .sidebar {
  background-color: #111827 !important;
  color: #F9FAFB !important;
  border-right-color: #374151 !important;
}

.force-dark-mode .topbar {
  background-color: #111827 !important;
  border-bottom-color: #374151 !important;
  height: 50px !important; /* Ensure consistent height in forced dark mode */
}

.force-dark-mode .MuiPaper-root {
  background-color: #374151 !important;
  color: #F9FAFB !important;
}

.force-dark-mode .MuiTab-root {
  color: #D1D5DB !important;
}

.force-dark-mode .MuiTab-root.Mui-selected {
  color: #60A5FA !important;
}

.force-dark-mode .MuiTabs-indicator {
  background-color: #60A5FA !important;
}
