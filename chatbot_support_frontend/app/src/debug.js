// Debug utility to check user role
export const debugUserRole = () => {
  try {
    // Get user data from localStorage
    const userData = localStorage.getItem('userData');
    
    if (userData) {
      const parsedUserData = JSON.parse(userData);
      console.log('User data from localStorage:', parsedUserData);
      console.log('User role:', parsedUserData.role);
      
      // Check if role is exactly 'admin' (case-sensitive)
      console.log('Is role exactly "admin"?', parsedUserData.role === 'admin');
      
      // Check if role is exactly 'Admin' (case-sensitive)
      console.log('Is role exactly "Admin"?', parsedUserData.role === 'Admin');
      
      // Check case-insensitive
      console.log('Is role "admin" (case-insensitive)?', 
        parsedUserData.role.toLowerCase() === 'admin');
      
      return parsedUserData;
    } else {
      console.log('No user data found in localStorage');
      return null;
    }
  } catch (error) {
    console.error('Error debugging user role:', error);
    return null;
  }
};
