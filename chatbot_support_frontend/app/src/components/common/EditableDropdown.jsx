import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../../contexts/ThemeContext';

/**
 * EditableDropdown component that allows selecting from a list of options
 * and adding new options.
 */
const EditableDropdown = ({
  id,
  label,
  value,
  onChange,
  options,
  onAddOption,
  placeholder = 'Select an option',
  addNewText = '+ Add new',
  disabled = false,
  required = false,
  error = null,
  className = '',
}) => {
  const { darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [isAddingNew, setIsAddingNew] = useState(false);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setIsAddingNew(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus input when adding new option
  useEffect(() => {
    if (isAddingNew && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAddingNew]);

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setIsAddingNew(false);
    }
  };

  const handleSelectOption = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  const handleAddNewClick = (e) => {
    e.stopPropagation();
    setIsAddingNew(true);
  };

  const handleAddNewSubmit = async (e) => {
    e.preventDefault();
    if (newValue.trim()) {
      try {
        await onAddOption(newValue.trim());
        onChange(newValue.trim());
        setNewValue('');
        setIsAddingNew(false);
        setIsOpen(false);
      } catch (error) {
        console.error('Error adding new option:', error);
      }
    }
  };

  const handleNewValueChange = (e) => {
    setNewValue(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      setIsAddingNew(false);
    }
  };

  // Find the selected option
  const selectedOption = options.find(option => option.value === value);

  return (
    <div className={`relative ${className}`} ref={dropdownRef} onKeyDown={handleKeyDown}>
      {label && (
        <label
          htmlFor={id}
          className={`block text-sm font-medium mb-1 ${
            darkMode ? 'text-gray-300' : 'text-gray-700'
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="relative">
        <button
          type="button"
          id={id}
          onClick={handleToggleDropdown}
          disabled={disabled}
          className={`w-full px-3 py-2 text-left border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white focus:ring-blue-500'
              : 'bg-white border-gray-300 text-gray-700 focus:ring-blue-500'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${
            error ? 'border-red-500' : ''
          }`}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {error && (
          <p className="mt-1 text-sm text-red-500">{error}</p>
        )}

        {isOpen && (
          <div
            className={`absolute z-10 mt-1 w-full rounded-md shadow-lg ${
              darkMode ? 'bg-gray-800' : 'bg-white'
            } max-h-60 overflow-auto`}
          >
            <ul
              className={`py-1 text-base ring-1 ring-black ring-opacity-5 ${
                darkMode ? 'text-white' : 'text-gray-900'
              }`}
              role="menu"
              aria-orientation="vertical"
              aria-labelledby={id}
            >
              {options.map((option) => (
                <li
                  key={option.value}
                  className={`cursor-pointer select-none relative py-2 pl-3 pr-9 ${
                    darkMode
                      ? option.value === value
                        ? 'bg-gray-700'
                        : 'hover:bg-gray-700'
                      : option.value === value
                      ? 'bg-blue-100'
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => handleSelectOption(option)}
                  role="menuitem"
                >
                  {option.label}
                  {option.value === value && (
                    <span
                      className={`absolute inset-y-0 right-0 flex items-center pr-4 ${
                        darkMode ? 'text-blue-400' : 'text-blue-600'
                      }`}
                    >
                      <svg
                        className="h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  )}
                </li>
              ))}
              
              {!isAddingNew ? (
                <li
                  className={`cursor-pointer select-none relative py-2 pl-3 pr-9 border-t ${
                    darkMode
                      ? 'border-gray-700 text-blue-400 hover:bg-gray-700'
                      : 'border-gray-200 text-blue-600 hover:bg-gray-100'
                  }`}
                  onClick={handleAddNewClick}
                  role="menuitem"
                >
                  {addNewText}
                </li>
              ) : (
                <li
                  className={`select-none relative py-2 pl-3 pr-9 border-t ${
                    darkMode ? 'border-gray-700' : 'border-gray-200'
                  }`}
                  role="menuitem"
                >
                  <form onSubmit={handleAddNewSubmit} className="flex">
                    <input
                      ref={inputRef}
                      type="text"
                      value={newValue}
                      onChange={handleNewValueChange}
                      className={`flex-grow px-2 py-1 text-sm border rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-700'
                      }`}
                      placeholder="Enter new value"
                    />
                    <button
                      type="submit"
                      className={`px-2 py-1 text-sm rounded-r-md focus:outline-none ${
                        darkMode
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                    >
                      Add
                    </button>
                  </form>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

EditableDropdown.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  onAddOption: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  addNewText: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
};

export default EditableDropdown;
