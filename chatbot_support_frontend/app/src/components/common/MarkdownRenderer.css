/* Markdown content styling */
.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Headers */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5rem;
}

.markdown-content h2 {
  font-size: 1.25rem;
}

.markdown-content h3 {
  font-size: 1.125rem;
}

/* Paragraphs */
.markdown-content p {
  margin-top: 0;
  margin-bottom: 0.75rem;
}

/* Lists */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin-top: 0;
  margin-bottom: 0.75rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content li > p {
  margin-top: 0.5rem;
}

.markdown-content li + li {
  margin-top: 0.25rem;
}

/* Code blocks */
.markdown-content pre {
  padding: 0.75rem;
  overflow: auto;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  background-color: #f3f4f6;
  margin-bottom: 0.75rem;
}

.dark .markdown-content pre {
  background-color: #374151;
}

.markdown-content code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 0.85em;
  border-radius: 0.25rem;
  background-color: #f3f4f6;
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
}

.dark .markdown-content code {
  background-color: #374151;
}

.markdown-content pre code {
  padding: 0;
  background-color: transparent;
}

/* Blockquotes */
.markdown-content blockquote {
  padding: 0 1rem;
  margin-left: 0;
  margin-right: 0;
  border-left: 0.25rem solid #e5e7eb;
  color: #6b7280;
}

.dark .markdown-content blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

/* Tables */
.markdown-content table {
  display: block;
  width: 100%;
  overflow: auto;
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 0.75rem;
}

.markdown-content table th {
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
}

.markdown-content table td {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
}

.dark .markdown-content table th,
.dark .markdown-content table td {
  border-color: #4b5563;
}

/* Links */
.markdown-content a {
  color: #3b82f6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.dark .markdown-content a {
  color: #60a5fa;
}

/* Images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 0.5rem 0;
}

/* Horizontal rule */
.markdown-content hr {
  height: 0.25rem;
  padding: 0;
  margin: 1.5rem 0;
  background-color: #e5e7eb;
  border: 0;
}

.dark .markdown-content hr {
  background-color: #4b5563;
}
