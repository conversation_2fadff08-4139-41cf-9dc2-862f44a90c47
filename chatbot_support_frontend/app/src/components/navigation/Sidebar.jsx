import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  UsersIcon,
  Cog6ToothIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useTheme } from '../../contexts/ThemeContext';

const Sidebar = ({ sidebarOpen, setSidebarOpen }) => {
  const theme = useTheme();

  // Debug theme state
  console.log('Sidebar - Theme state:', {
    darkMode: theme.darkMode,
    themeColors: theme.colors
  });

  // Navigation items
  const navItems = [
    {
      name: 'Dashboard',
      icon: HomeIcon,
      path: '/admin/dashboard',
    },
    {
      name: 'Chat',
      icon: ChatBubbleLeftRightIcon,
      path: '/admin/chat',
    },
    {
      name: 'User & Group Management',
      icon: UsersIcon,
      path: '/admin/users',
    },
    {
      name: 'Admin Settings',
      icon: Cog6ToothIcon,
      path: '/admin/settings',
    },
  ];

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {sidebarOpen
      // && (
      //   <div
      //     style={{
      //       position: 'fixed',
      //       top: 0,
      //       left: 0,
      //       right: 0,
      //       bottom: 0,
      //       backgroundColor: 'rgba(0, 0, 0, 0.5)',
      //       zIndex: 40
      //     }}
      //     onClick={() => setSidebarOpen(false)}
      //   ></div>
      // )
      }

      {/* Sidebar */}
      <div className={`sidebar ${sidebarOpen ? 'open' : ''}`}>
        {/* Sidebar header */}
        <div className="sidebar-header">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/logo.svg"
              alt="Logo"
              style={{ height: '2rem', width: '2rem', marginRight: '0.5rem' }}
            />
            <span style={{ fontSize: '1.125rem', fontWeight: 600 }}>Admin Panel</span>
          </div>

          {/* Close button (mobile only) */}
          <button
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: theme.colors.text.tertiary
            }}
            onClick={() => setSidebarOpen(false)}
          >
            <XMarkIcon style={{ height: '1.5rem', width: '1.5rem' }} />
          </button>
        </div>

        {/* Navigation */}
        <nav className="sidebar-nav">
          <ul>
            {navItems.map((item) => (
              <li key={item.name}>
                <NavLink
                  to={item.path}
                  className={({ isActive }) => isActive ? 'active' : ''}
                >
                  <item.icon style={{ height: '1.25rem', width: '1.25rem', marginRight: '0.75rem' }} />
                  <span>{item.name}</span>
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar footer */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          padding: '1rem',
          borderTop: `1px solid ${theme.colors.border.primary}`,
          fontSize: '0.75rem',
          color: theme.colors.text.tertiary
        }}>
          <p>© 2025 Admin Panel</p>
          <p>Version 1.0.0</p>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
