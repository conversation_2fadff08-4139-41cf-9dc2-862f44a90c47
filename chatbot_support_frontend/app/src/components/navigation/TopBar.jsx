import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Bars3Icon,
  SunIcon,
  MoonIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import NotificationBell from '../notifications/NotificationBell';

const TopBar = ({ toggleSidebar }) => {
  const [profileOpen, setProfileOpen] = useState(false);
  const { logout, user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();

  // We no longer need mock notifications as we're using the NotificationBell component

  // Debug theme state
  console.log('TopBar - Theme state:', {
    darkMode: theme.darkMode,
    toggleFunction: typeof theme.toggleDarkMode,
    themeColors: theme.colors
  });

  // Handle theme toggle with debug
  const handleThemeToggle = () => {
    console.log('Theme toggle clicked, current darkMode:', theme.darkMode);
    theme.toggleDarkMode();
    console.log('After toggle, darkMode should be:', !theme.darkMode);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/login');
    console.log('Logged out successfully');
  };

  return (
    <header className="topbar">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* Left: Hamburger menu (mobile only) */}
        <button
          className="mobile-menu-btn"
          onClick={toggleSidebar}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            marginRight: '1rem'
          }}
        >
          <Bars3Icon style={{ height: '1.5rem', width: '1.5rem' }} />
        </button>

        <h1 style={{ fontSize: '1.25rem', fontWeight: 600, display: 'none' }}
            className="sm-visible">Admin Dashboard</h1>
      </div>

      {/* Right: User menu, notifications, theme toggle */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        {/* Theme toggle */}
        <button
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            padding: '0.5rem',
            borderRadius: '9999px'
          }}
          onClick={handleThemeToggle}
          aria-label="Toggle dark mode"
        >
          {theme.darkMode ? (
            <SunIcon style={{ height: '1.25rem', width: '1.25rem', color: '#FBBF24' }} />
          ) : (
            <MoonIcon style={{ height: '1.25rem', width: '1.25rem', color: '#6B7280' }} />
          )}
        </button>

        {/* Notifications */}
        <NotificationBell />

        {/* User profile */}
        <div style={{ position: 'relative' }}>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'none',
              border: 'none',
              cursor: 'pointer'
            }}
            onClick={() => setProfileOpen(!profileOpen)}
          >
            <div style={{
              height: '2rem',
              width: '2rem',
              borderRadius: '9999px',
              backgroundColor: theme.colors.background.tertiary,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <UserCircleIcon style={{
                height: '1.75rem',
                width: '1.75rem',
                color: theme.colors.text.tertiary
              }} />
            </div>
            <span style={{
              display: 'none',
              fontSize: '0.875rem',
              fontWeight: 500
            }} className="md-visible">{user?.username || 'User'}</span>
          </button>

          {/* Profile dropdown */}
          {profileOpen && (
            <div style={{
              position: 'absolute',
              right: 0,
              marginTop: '0.5rem',
              width: '12rem',
              borderRadius: '0.375rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              backgroundColor: theme.colors.background.primary,
              border: `1px solid ${theme.colors.border.primary}`,
              zIndex: 50
            }}>
              <div style={{
                padding: '0.75rem 1rem',
                borderBottom: `1px solid ${theme.colors.border.primary}`
              }}>
                <p style={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.colors.text.primary
                }}>{user?.username || 'User'}</p>
                <p style={{
                  fontSize: '0.75rem',
                  color: theme.colors.text.tertiary,
                  marginTop: '0.25rem'
                }}>{user?.role || 'user'}</p>
              </div>
              <a
                href="/admin/profile"
                style={{
                  display: 'block',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  textDecoration: 'none',
                  color: theme.colors.text.primary,
                  transition: 'background-color 0.2s',
                  ':hover': {
                    backgroundColor: theme.colors.background.secondary
                  }
                }}
              >
                Your Profile
              </a>
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  textAlign: 'left',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  color: theme.colors.error.primary,
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
                onClick={handleLogout}
              >
                <ArrowRightOnRectangleIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default TopBar;
