import React from 'react';
import { ArrowTopRightOnSquareIcon as ExternalLinkIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

/**
 * Component to display document references in chat messages
 *
 * @param {Object} props
 * @param {Array} props.references - Array of document references
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 */
const DocumentReferences = ({ references = [], darkMode = false }) => {
  // If no references, don't render anything
  if (!references || references.length === 0) {
    return null;
  }

  // Log references for debugging
  console.log('Document references:', references);

  // Function to format page number display
  const formatPageDisplay = (ref) => {
    if (ref.page !== null && ref.page !== undefined) {
      return ` (Page ${ref.page})`;
    }
    return '';
  };

  // Function to get a clean document name (without extension)
  const getDocumentName = (filename) => {
    if (!filename) return "Unknown Document";

    // Remove file extension
    const nameParts = filename.split('.');
    if (nameParts.length > 1) {
      nameParts.pop(); // Remove the last part (extension)
    }

    // Join back and capitalize words
    return nameParts.join('.')
      .split(/[_-]/) // Split by underscores or hyphens
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <div className="document-references" style={{
      marginTop: '0.75rem',
      borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
      paddingTop: '0.5rem',
      fontSize: '0.75rem',
      color: darkMode ? '#9CA3AF' : '#6B7280'
    }}>
      <div style={{ marginBottom: '0.25rem', fontWeight: '500' }}>
        Sources:
      </div>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
        {references.map((ref, index) => (
          <a
            key={`${ref.document_id}-${index}`}
            href={ref.url || '#'}
            onClick={(e) => {
              if (!ref.url) {
                e.preventDefault();
                console.log('Document URL not available:', ref);
                alert(`Document URL not available for: ${ref.filename}`);
              } else {
                console.log('Opening document URL:', ref.url);
                // If URL is available, let the default link behavior work
                window.open(ref.url, '_blank');
              }
            }}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '0.25rem 0.5rem',
              borderRadius: '0.25rem',
              backgroundColor: darkMode ? '#374151' : '#F3F4F6',
              border: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              textDecoration: 'none',
              color: 'inherit',
              cursor: ref.url ? 'pointer' : 'not-allowed',
              transition: 'all 0.2s ease',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              ':hover': {
                backgroundColor: darkMode ? '#4B5563' : '#E5E7EB',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
            title={ref.url ? `Open ${ref.filename}${formatPageDisplay(ref)}` : `Document URL not available for: ${ref.filename}`}
          >
            <DocumentTextIcon style={{
              width: '1rem',
              height: '1rem',
              marginRight: '0.5rem',
              color: ref.url ? (darkMode ? '#60A5FA' : '#2563EB') : (darkMode ? '#9CA3AF' : '#9CA3AF')
            }} />
            <span style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '150px',
              fontWeight: '500'
            }}>
              {getDocumentName(ref.filename)}{formatPageDisplay(ref)}
            </span>
            <ExternalLinkIcon style={{
              width: '0.875rem',
              height: '0.875rem',
              marginLeft: '0.5rem',
              color: ref.url ? (darkMode ? '#60A5FA' : '#2563EB') : (darkMode ? '#9CA3AF' : '#9CA3AF')
            }} />
          </a>
        ))}
      </div>
    </div>
  );
};

export default DocumentReferences;
