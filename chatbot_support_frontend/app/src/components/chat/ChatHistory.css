:root {
  --sidebar-width: 280px;
  --sidebar-expanded-width: 350px;
  --transition-speed: 0.3s;
  --border-color: #E5E7EB;
  --hover-color: #F3F4F6;
  --active-color: #E5E7EB;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --accent-color: #3B82F6;
  --accent-hover: #2563EB;
}

.dark {
  --border-color: #4B5563;
  --hover-color: #374151;
  --active-color: #4B5563;
  --text-primary: #F9FAFB;
  --text-secondary: #9CA3AF;
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --accent-color: #3B82F6;
  --accent-hover: #2563EB;
}

.chat-history {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: var(--sidebar-width);
  overflow: hidden;
  transition: width var(--transition-speed) ease;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
}

.chat-history.expanded {
  width: var(--sidebar-expanded-width);
}

/* Admin specific chat history */
.chat-history.in-admin-page {
  height: calc(100vh - 65px); /* Adjusted height to fit in viewport */
  max-height: calc(100vh - 65px); /* Adjusted height to fit in viewport */
  overflow-y: auto;
}

.chat-history-header {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
}

.chat-history-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chat-history-title h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  color: var(--text-secondary);
}

.expand-button:hover {
  background-color: var(--hover-color);
}

.expand-icon {
  height: 1rem;
  width: 1rem;
}

.new-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.new-chat-button:hover {
  background-color: var(--accent-hover);
}

.new-chat-icon {
  height: 1rem;
  width: 1rem;
  margin-right: 0.25rem;
}

.chat-history-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  scrollbar-width: thin;
  max-height: calc(100% - 80px); /* Fixed height for scrolling */
}

.chat-history-list::-webkit-scrollbar {
  width: 6px;
}

.chat-history-list::-webkit-scrollbar-track {
  background: transparent;
}

.chat-history-list::-webkit-scrollbar-thumb {
  background-color: var(--text-secondary);
  border-radius: 3px;
  opacity: 0.7;
}

.chat-history-list::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-primary);
  opacity: 1;
}

/* Admin specific chat history list */
.admin-page .chat-history-list {
  max-height: calc(100vh - 140px); /* Fixed height for admin */
  height: auto;
  overflow-y: auto;
}

.conversation-group {
  margin-bottom: 1rem;
}

.date-header {
  font-size: 0.75rem;
  color: var(--text-secondary);
  padding: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.25rem;
}

.conversation-item:hover {
  background-color: var(--hover-color);
}

.conversation-item.active {
  background-color: var(--active-color);
}

.conversation-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.conversation-icon {
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.5rem;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.conversation-details {
  overflow: hidden;
  flex: 1;
}

.conversation-title {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.conversation-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.conversation-username {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  font-style: italic;
}

.username-icon {
  height: 0.875rem;
  width: 0.875rem;
  margin-right: 0.25rem;
  color: var(--text-secondary);
}

.message-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.no-conversations {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 0.75rem 0;
  position: sticky;
  bottom: 0;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  margin-top: 0.5rem;
}

.load-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  background-color: var(--hover-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.load-more-button:hover {
  background-color: var(--active-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.load-more-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.load-more-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.load-more-icon {
  height: 0.875rem;
  width: 0.875rem;
  margin-left: 0.25rem;
  transition: transform 0.2s ease;
}

.load-more-button:hover .load-more-icon {
  transform: translateY(2px);
}

/* Delete button styles */
.delete-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  opacity: 0.5;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-button:hover {
  opacity: 1;
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.delete-icon {
  width: 1rem;
  height: 1rem;
}

/* Delete confirmation styles */
.delete-confirm {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-yes, .delete-no {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-yes {
  color: #ef4444;
}

.delete-yes:hover {
  background-color: rgba(239, 68, 68, 0.2);
}

.delete-no {
  color: var(--text-secondary);
}

.delete-no:hover {
  background-color: var(--hover-color);
}

.cancel-icon {
  width: 1rem;
  height: 1rem;
}
