import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from 'recharts';
import { Box, Typography } from '@mui/material';

const NotificationDonutChart = ({ readCount, unreadCount }) => {
  const data = [
    { name: 'Read', value: readCount, color: '#4caf50' },
    { name: 'Unread', value: unreadCount, color: '#f44336' }
  ];

  const total = readCount + unreadCount;
  const readPercentage = Math.round((readCount / (total || 1)) * 100);

  const COLORS = ['#4caf50', '#f44336'];

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const item = payload[0];
      return (
        <Box sx={{ bgcolor: 'background.paper', p: 1.5, boxShadow: 1, borderRadius: 1 }}>
          <Typography variant="body2" fontWeight="medium">{`${item.name}: ${item.value}`}</Typography>
          <Typography variant="body2" color="text.secondary">{`${(item.value / total * 100).toFixed(1)}%`}</Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ height: 300, position: 'relative' }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            paddingAngle={5}
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
      <Box 
        sx={{ 
          position: 'absolute', 
          top: '50%', 
          left: '50%', 
          transform: 'translate(-50%, -50%)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        <Typography variant="h4" component="div" fontWeight="bold">
          {readPercentage}%
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Read
        </Typography>
      </Box>
    </Box>
  );
};

export default NotificationDonutChart;
