import React, { useState, useMemo } from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Fade,
  Divider
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import NotificationDonutChart from './charts/NotificationDonutChart';
import UserReadBarChart from './charts/UserReadBarChart';
import NotificationTypeChart from './charts/NotificationTypeChart';
import NotificationCard from './NotificationCard';

const NotificationDashboard = ({ stats, refreshData, isRefreshing }) => {
  const [showCharts, setShowCharts] = useState(true);

  // Filter out duplicate notifications
  const uniqueNotifications = useMemo(() => {
    const seen = new Map();
    return stats.notifications.filter(notification => {
      // Create a unique key based on document_id and type
      const key = `${notification.document_id}-${notification.type}`;

      // If we haven't seen this key before, keep the notification
      if (!seen.has(key)) {
        seen.set(key, true);
        return true;
      }

      return false;
    });
  }, [stats.notifications]);

  // Prepare summary data for users
  const prepareSummaryData = () => {
    const userReadCounts = {};

    // Initialize counts for each user
    stats.users.forEach(user => {
      userReadCounts[user.id] = {
        id: user.id,
        username: user.username,
        read: 0,
        unread: 0,
        total: uniqueNotifications.length
      };
    });

    // Count read/unread for each user
    stats.read_status.forEach(status => {
      // Check if this status corresponds to a notification in our unique list
      const notificationExists = uniqueNotifications.some(n => n.id === status.notification_id);

      if (userReadCounts[status.user_id] && notificationExists) {
        userReadCounts[status.user_id].read += 1;
        userReadCounts[status.user_id].unread =
          userReadCounts[status.user_id].total - userReadCounts[status.user_id].read;
      }
    });

    return Object.values(userReadCounts).map(user => ({
      ...user,
      readPercentage: Math.round((user.read / user.total) * 100)
    }));
  };

  // Get document name for a notification
  const getDocumentName = (documentId) => {
    return stats.documents?.find(doc => doc?.id === documentId)?.filename || 'N/A';
  };

  const summaryData = prepareSummaryData();

  const handleRefresh = () => {
    setShowCharts(false);
    refreshData();
    setTimeout(() => {
      setShowCharts(true);
    }, 300);
  };

  return (
    <Box>
      {/* Header with refresh button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Notification Read Status
        </Typography>
        <Tooltip title="Refresh data">
          <IconButton
            onClick={handleRefresh}
            disabled={isRefreshing}
            sx={{
              animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Stats cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderLeft: '4px solid #3f51b5',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'scale(1.02)' }
            }}
          >
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '50%',
                  bgcolor: 'rgba(63, 81, 181, 0.1)',
                  mr: 2
                }}
              >
                <NotificationsIcon color="primary" />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Total Notifications
                </Typography>
                <Typography variant="h5" component="div">
                  {stats.summary.total_notifications}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderLeft: '4px solid #9c27b0',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'scale(1.02)' }
            }}
          >
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '50%',
                  bgcolor: 'rgba(156, 39, 176, 0.1)',
                  mr: 2
                }}
              >
                <PersonIcon sx={{ color: '#9c27b0' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Total Users
                </Typography>
                <Typography variant="h5" component="div">
                  {stats.summary.total_users}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderLeft: '4px solid #4caf50',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'scale(1.02)' }
            }}
          >
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '50%',
                  bgcolor: 'rgba(76, 175, 80, 0.1)',
                  mr: 2
                }}
              >
                <CheckCircleIcon sx={{ color: '#4caf50' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Read Count
                </Typography>
                <Typography variant="h5" component="div">
                  {stats.summary.read_count}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderLeft: '4px solid #f44336',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'scale(1.02)' }
            }}
          >
            <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '50%',
                  bgcolor: 'rgba(244, 67, 54, 0.1)',
                  mr: 2
                }}
              >
                <CancelIcon sx={{ color: '#f44336' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Unread Count
                </Typography>
                <Typography variant="h5" component="div">
                  {stats.summary.unread_count}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Fade in={showCharts} timeout={500}>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Read Status Overview
                </Typography>
                <NotificationDonutChart
                  readCount={stats.summary.read_count}
                  unreadCount={stats.summary.unread_count}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Users by Read Status
                </Typography>
                <UserReadBarChart userData={summaryData} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Fade>

      {/* Notification Type Distribution */}
      <Fade in={showCharts} timeout={500} style={{ transitionDelay: '150ms' }}>
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Notification Type Distribution
            </Typography>
            <NotificationTypeChart notifications={uniqueNotifications} />
          </CardContent>
        </Card>
      </Fade>

      {/* Recent Notifications */}
      <Fade in={showCharts} timeout={500} style={{ transitionDelay: '300ms' }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Notifications
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {uniqueNotifications.slice(0, 5).map((notification) => (
                <NotificationCard
                  key={notification.id}
                  notification={notification}
                  documentName={getDocumentName(notification.document_id)}
                />
              ))}
            </Box>
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default NotificationDashboard;
