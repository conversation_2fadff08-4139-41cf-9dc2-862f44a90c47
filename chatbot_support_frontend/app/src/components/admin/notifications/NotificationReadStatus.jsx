import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Tabs, Tab, CircularProgress } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ViewListIcon from '@mui/icons-material/ViewList';
import SummarizeIcon from '@mui/icons-material/Summarize';
import NotificationDashboard from './NotificationDashboard';
import NotificationDetails from './NotificationDetails';
import NotificationSummary from './NotificationSummary';
import notificationAPI from '../../../services/notificationAPI';
import { useTheme } from '../../../contexts/ThemeContext';

// TabPanel component to handle tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notification-tabpanel-${index}`}
      aria-labelledby={`notification-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const NotificationReadStatus = () => {
  // Get theme from context
  const appTheme = useTheme();

  // Create Material UI theme based on our app theme
  const muiTheme = createTheme({
    palette: {
      mode: appTheme.darkMode ? 'dark' : 'light',
      primary: {
        main: appTheme.colors.accent.primary,
      },
      secondary: {
        main: appTheme.colors.accent.secondary,
      },
      background: {
        default: appTheme.colors.background.primary,
        paper: appTheme.colors.background.secondary,
      },
      text: {
        primary: appTheme.colors.text.primary,
        secondary: appTheme.colors.text.secondary,
      },
    },
  });

  // State
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch notification stats
  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const data = await notificationAPI.getNotificationStats();
      setStats(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching notification statistics:', err);
      setError('Failed to load notification statistics');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      const data = await notificationAPI.getNotificationStats();
      setStats(data);
      setError(null);
    } catch (err) {
      console.error('Error refreshing notification statistics:', err);
      setError('Failed to refresh notification statistics');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Wrap all returns with ThemeProvider
  const renderContent = () => {
    if (loading) {
      return (
        <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 400 }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading notification data...
          </Typography>
        </Paper>
      );
    }

    if (error) {
      return (
        <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 400 }}>
          <Typography variant="h6" color="error">
            {error}
          </Typography>
          <Box sx={{ mt: 2 }}>
            <button
              onClick={refreshData}
              style={{
                padding: '8px 16px',
                backgroundColor: appTheme.colors.error.primary,
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Try Again
            </button>
          </Box>
        </Paper>
      );
    }

    if (!stats || !stats.notifications || stats.notifications.length === 0) {
      return (
        <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 400 }}>
          <Typography variant="h6">
            No notification data available
          </Typography>
        </Paper>
      );
    }

    return (
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="notification tabs"
            centered
          >
            <Tab
              icon={<DashboardIcon />}
              label="Dashboard"
              id="notification-tab-0"
              aria-controls="notification-tabpanel-0"
              sx={{ minWidth: 120 }}
            />
            <Tab
              icon={<ViewListIcon />}
              label="Details"
              id="notification-tab-1"
              aria-controls="notification-tabpanel-1"
              sx={{ minWidth: 120 }}
            />
            <Tab
              icon={<SummarizeIcon />}
              label="Summary"
              id="notification-tab-2"
              aria-controls="notification-tabpanel-2"
              sx={{ minWidth: 120 }}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <NotificationDashboard
            stats={stats}
            refreshData={refreshData}
            isRefreshing={isRefreshing}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <NotificationDetails
            stats={stats}
            refreshData={refreshData}
            isRefreshing={isRefreshing}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <NotificationSummary
            stats={stats}
            refreshData={refreshData}
            isRefreshing={isRefreshing}
          />
        </TabPanel>
      </Paper>
    );
  };

  // Return the content wrapped in ThemeProvider
  return (
    <ThemeProvider theme={muiTheme}>
      <CssBaseline />
      {renderContent()}
    </ThemeProvider>
  );
};

export default NotificationReadStatus;
