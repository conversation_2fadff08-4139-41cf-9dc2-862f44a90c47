import { useEffect, useState } from 'react';
import { sessionAPI } from '../../services/sessionAPI';
import { useAuth } from '../../contexts/AuthContext';

// SessionPersistence component to handle session restoration on page load
const SessionPersistence = () => {
  const [initialized, setInitialized] = useState(false);
  const [lastRefreshAttempt, setLastRefreshAttempt] = useState(0);
  const { isAuthenticated } = useAuth();

  // Check for session on initial load
  useEffect(() => {
    const checkSession = async () => {
      if (!initialized) {
        console.log('Checking session on page load...');

        try {
          // Only try to refresh if we have a token in localStorage (for backward compatibility)
          const token = localStorage.getItem('authToken');
          if (token) {
            // Try to refresh the session using the existing token
            const result = await sessionAPI.refreshSession();
            if (result.success !== false) {
              console.log('Session refreshed successfully on page load');
            } else {
              console.log('Session refresh failed, but continuing without error');
            }
          } else {
            console.log('No token found, skipping session refresh');
          }
        } catch (error) {
          console.log('Error refreshing session on page load, but continuing:', error);
        }

        setInitialized(true);
      }
    };

    checkSession();
  }, [initialized]);

  // Set up periodic token refresh only if authenticated
  useEffect(() => {
    // Don't set up refresh if not authenticated
    if (!isAuthenticated) return;

    // Function to refresh token with rate limiting
    const attemptTokenRefresh = async () => {
      const now = Date.now();
      // Don't refresh more than once every 30 seconds
      if (now - lastRefreshAttempt < 30000) {
        console.log('Skipping refresh, too soon since last attempt');
        return;
      }

      setLastRefreshAttempt(now);
      console.log('Performing token refresh check...');

      try {
        // Refresh the session using the existing token
        const result = await sessionAPI.refreshSession();
        if (result.success !== false) {
          console.log('Successfully refreshed token');
        } else {
          console.log('Token refresh failed, but continuing without error');
        }
      } catch (error) {
        console.log('Error during token refresh, but continuing:', error);
      }
    };

    // Set up interval for token refresh (every 5 minutes)
    const refreshInterval = setInterval(attemptTokenRefresh, 5 * 60 * 1000);

    // Clean up interval on unmount
    return () => clearInterval(refreshInterval);
  }, [lastRefreshAttempt, isAuthenticated]);

  return null; // This component doesn't render anything
};

export default SessionPersistence;
