/* Notification Bell Container */
.notification-bell-container {
  position: relative;
}

/* Notification Bell Button */
.notification-bell-button {
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.notification-bell-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .notification-bell-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* New notifications indicator */
.notification-bell-button.has-new-notifications {
  animation: highlight-button 2s infinite alternate;
}

@keyframes highlight-button {
  0% {
    background-color: rgba(59, 130, 246, 0.1);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.2);
  }
}

.dark .notification-bell-button.has-new-notifications {
  animation: highlight-button-dark 2s infinite alternate;
}

@keyframes highlight-button-dark {
  0% {
    background-color: rgba(96, 165, 250, 0.1);
  }
  100% {
    background-color: rgba(96, 165, 250, 0.2);
  }
}

/* Notification Bell Icon */
.notification-bell-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--text-primary);
}

/* Pulsing animation for new notifications */
.notification-bell-icon.pulse {
  animation: pulse-icon 1s infinite alternate;
}

@keyframes pulse-icon {
  0% {
    transform: scale(1);
    color: var(--text-primary);
  }
  100% {
    transform: scale(1.1);
    color: var(--accent-color);
  }
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  min-width: 1rem;
  height: 1rem;
  padding: 0.125rem 0.25rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Notification Dropdown */
.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  max-width: 90vw;
  max-height: 500px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 50;
  overflow: hidden;
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
}

.notification-dropdown.dark {
  background-color: #1f2937;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
}

/* Notification Header */
.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.notification-dropdown.dark .notification-header {
  border-bottom-color: #374151;
}

.notification-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mark-all-read-button {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.mark-all-read-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.notification-dropdown.dark .mark-all-read-button {
  color: #60a5fa;
}

.notification-dropdown.dark .mark-all-read-button:hover {
  background-color: rgba(96, 165, 250, 0.1);
}

/* Notification List */
.notification-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

/* Notification Item */
.notification-item {
  display: flex;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
  cursor: pointer;
}

.notification-dropdown.dark .notification-item {
  border-bottom-color: #374151;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-dropdown.dark .notification-item:hover {
  background-color: #2d3748;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-dropdown.dark .notification-item.unread {
  background-color: #1e3a8a;
}

.notification-item.unread:hover {
  background-color: #e0f2fe;
}

.notification-dropdown.dark .notification-item.unread:hover {
  background-color: #1e40af;
}

/* Notification Icon */
.notification-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Notification Content */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.notification-message {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  word-break: break-word;
}

.notification-meta {
  display: flex;
  font-size: 0.7rem;
  color: var(--text-tertiary);
}

.notification-time {
  margin-right: 0.5rem;
}

.notification-creator {
  font-style: italic;
}

/* Notification Actions */
.notification-actions {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.notification-action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  margin-left: 0.25rem;
}

.notification-action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.notification-dropdown.dark .notification-action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notification-action-icon {
  width: 1rem;
  height: 1rem;
  color: var(--text-secondary);
}

/* Loading and Empty States */
.notification-loading,
.notification-empty {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}
