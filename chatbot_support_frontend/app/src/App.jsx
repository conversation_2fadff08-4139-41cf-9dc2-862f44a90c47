import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';

// Styles
import './styles/variables.css';
import './styles/FixScrollbars.css';
import './styles/theme-variables.css';
import './styles/global-theme.css';
import './styles/dark-mode-fix.css';

// Layouts
import AdminLayout from './layouts/AdminLayout';
import UserLayout from './layouts/UserLayout';

// Admin Pages
import Dashboard from './pages/admin/Dashboard';
import Chat from './pages/admin/Chat';
import UserManagement from './pages/admin/UserManagement';
import Settings from './pages/admin/Settings';

// User Pages
import UserChat from './pages/user/UserChat';
import UserDocuments from './pages/user/UserDocuments';

// Auth Pages
import Login from './pages/auth/Login';
import SessionPersistence from './components/auth/SessionPersistence';

// Contexts
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { UserProvider } from './contexts/UserContext';
import { DocumentProvider } from './contexts/DocumentContext';
import { ChatProvider } from './contexts/ChatContext';
import { NotificationProvider } from './contexts/NotificationContext';
import ThemeProvider from './contexts/ThemeContext';

// Utilities
import { debugUserRole } from './debug';

// Loading spinner component
const LoadingSpinner = () => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    flexDirection: 'column',
    backgroundColor: '#f9fafb'
  }}>
    <div style={{
      width: '50px',
      height: '50px',
      border: '5px solid #e5e7eb',
      borderTopColor: '#3b82f6',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginBottom: '1rem'
    }} />
    <style>
      {`
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
      `}
    </style>
    <p style={{ color: '#6b7280', fontSize: '1rem' }}>Loading application...</p>
  </div>
);

// Protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute - isAuthenticated:', isAuthenticated);
  console.log('ProtectedRoute - loading:', loading);
  console.log('ProtectedRoute - user:', user);
  console.log('ProtectedRoute - location:', location.pathname);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated || !user) {
    console.log('Not authenticated, redirecting to login with state:', { from: location });
    // Save the location they were trying to go to
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user is in the correct route type
  const isAdminRoute = location.pathname.startsWith('/admin');
  const isUserRoute = location.pathname.startsWith('/chat');

  if (isAdminRoute && user.role?.toLowerCase() !== 'admin') {
    console.log('Non-admin user trying to access admin route, redirecting to chat');
    return <Navigate to="/chat" replace />;
  }

  if (isUserRoute && user.role?.toLowerCase() === 'admin') {
    // This is optional - you might want admins to be able to access user routes
    // If you want to enforce separation, uncomment this:
    // console.log('Admin user trying to access user route, redirecting to admin dashboard');
    // return <Navigate to="/admin/dashboard" replace />;
  }

  return children;
};

// Role-based route component
const RoleBasedRoute = () => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // If still loading, show spinner
  if (loading) {
    return <LoadingSpinner />;
  }

  // Debug user role
  console.log('RoleBasedRoute - User from context:', user);
  console.log('RoleBasedRoute - Current location:', location);
  debugUserRole(); // Call the debug function without storing the result

  // Check if we're already on a valid path
  const currentPath = location.pathname;
  const isAdminPath = currentPath.startsWith('/admin/');
  const isChatPath = currentPath === '/chat' || currentPath.startsWith('/chat/');

  // If we're already on a valid path for the user's role, stay there
  if (user?.role?.toLowerCase() === 'admin' && isAdminPath) {
    console.log('Already on admin path, staying here');
    return null; // Don't navigate away
  } else if (user && isChatPath) {
    console.log('Already on chat path, staying here');
    return null; // Don't navigate away
  }

  // Otherwise, redirect based on user role (case-insensitive check)
  if (user?.role?.toLowerCase() === 'admin') {
    console.log('Redirecting to admin dashboard');
    return <Navigate to="/admin/dashboard" replace />;
  } else if (user) {
    console.log('Redirecting to chat');
    return <Navigate to="/chat" replace />;
  } else {
    // If no user, redirect to login
    console.log('No user, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
};

// Admin routes with layout
const AdminRoutes = () => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Debug user role
  console.log('AdminRoutes - User from context:', user);
  console.log('AdminRoutes - Current location:', location);
  debugUserRole();

  // If still loading, show spinner
  if (loading) {
    return <LoadingSpinner />;
  }

  // Check if user is admin (case-insensitive check)
  if (!user) {
    console.log('No user, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (user.role.toLowerCase() !== 'admin') {
    console.log('Not an admin, redirecting to chat');
    return <Navigate to="/chat" replace />;
  }

  console.log('User is admin, showing admin dashboard');
  return (
    <ProtectedRoute>
      <UserProvider>
        <DocumentProvider>
          <ChatProvider>
            <Routes>
              <Route path="/" element={<AdminLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="users" element={<UserManagement />} />
                <Route path="chat" element={<Chat />} />
                <Route path="settings" element={<Settings />} />
                <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
              </Route>
            </Routes>
          </ChatProvider>
        </DocumentProvider>
      </UserProvider>
    </ProtectedRoute>
  );
};

// User routes with layout
const UserRoutes = () => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Debug user info
  console.log('UserRoutes - User from context:', user);
  console.log('UserRoutes - Current location:', location);

  // If still loading, show spinner
  if (loading) {
    return <LoadingSpinner />;
  }

  // If no user, redirect to login
  if (!user) {
    console.log('No user, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If user is admin, they should be in admin routes
  if (user.role?.toLowerCase() === 'admin') {
    console.log('Admin user in user routes, redirecting to admin dashboard');
    return <Navigate to="/admin/dashboard" replace />;
  }

  console.log('User is regular user, showing chat interface');
  return (
    <ProtectedRoute>
      <UserProvider>
        <DocumentProvider>
          <ChatProvider>
            <Routes>
              <Route path="/" element={<UserLayout />}>
                <Route index element={<UserChat />} />
                <Route path="documents" element={<UserDocuments />} />
                <Route path="*" element={<Navigate to="/chat" replace />} />
              </Route>
            </Routes>
          </ChatProvider>
        </DocumentProvider>
      </UserProvider>
    </ProtectedRoute>
  );
};

const App = () => {
  return (
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <NotificationProvider>
            {/* Session persistence component */}
            <SessionPersistence />

            <Routes>
              {/* Auth routes */}
              <Route path="/login" element={<Login />} />

              {/* Admin routes */}
              <Route path="/admin/*" element={<AdminRoutes />} />

              {/* User routes */}
              <Route path="/chat/*" element={<UserRoutes />} />

              {/* Redirect root based on user role */}
              <Route path="/" element={<RoleBasedRoute />} />

              {/* 404 route */}
              <Route path="*" element={
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100vh'
                }}>
                  <h1 style={{
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    marginBottom: '1rem'
                  }}>404</h1>
                  <p style={{
                    fontSize: '1.25rem',
                    marginBottom: '2rem'
                  }}>Page not found</p>
                  <a
                    href="/"
                    style={{
                      padding: '0.5rem 1rem',
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      borderRadius: '0.375rem',
                      textDecoration: 'none'
                    }}
                  >
                    Go Home
                  </a>
                </div>
              } />
            </Routes>
          </NotificationProvider>
        </AuthProvider>
      </ThemeProvider>
    </Router>
  );
};

export default App;
