import React, { useState } from 'react';
import Sidebar from '../components/navigation/Sidebar';
import TopBar from '../components/navigation/TopBar';
import { Outlet } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const AdminLayout = () => {
  const theme = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Toggle sidebar on mobile
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="app-container" style={{
      overflow: 'hidden',
      backgroundColor: theme.colors.background.primary,
      color: theme.colors.text.primary
    }}>
      {/* Sidebar */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main content area */}
      <div className="main-content" style={{
        overflow: 'hidden',
        maxWidth: '100%',
        backgroundColor: theme.colors.background.primary
      }}>
        <TopBar
          toggleSidebar={toggleSidebar}
        />

        {/* Page content */}
        <main style={{
          overflow: 'hidden',
          maxWidth: '100%',
          padding: '0',
          backgroundColor: theme.colors.background.primary
        }}>
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
