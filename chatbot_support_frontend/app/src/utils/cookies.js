// Utility functions for working with cookies

// Get a cookie by name
export const getCookie = (name) => {
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith(name + '=')) {
      return cookie.substring(name.length + 1);
    }
  }
  return null;
};

// Set a cookie
export const setCookie = (name, value, options = {}) => {
  const { expires, path, domain, secure, sameSite } = options;
  
  let cookieString = `${name}=${value}`;
  
  if (expires) {
    if (typeof expires === 'number') {
      const date = new Date();
      date.setTime(date.getTime() + (expires * 24 * 60 * 60 * 1000));
      cookieString += `; expires=${date.toUTCString()}`;
    } else {
      cookieString += `; expires=${expires.toUTCString()}`;
    }
  }
  
  if (path) cookieString += `; path=${path}`;
  if (domain) cookieString += `; domain=${domain}`;
  if (secure) cookieString += '; secure';
  if (sameSite) cookieString += `; samesite=${sameSite}`;
  
  document.cookie = cookieString;
};

// Delete a cookie
export const deleteCookie = (name, options = {}) => {
  const { path, domain } = options;
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  
  if (path) cookieString += `; path=${path}`;
  if (domain) cookieString += `; domain=${domain}`;
  
  document.cookie = cookieString;
};
