import React, { useState } from 'react';
import {
  ArrowPathIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';
import { settingsAPI } from '../../services/settingsAPI';

const Settings = () => {
  // Get dark mode from context or props
  const darkMode = document.documentElement.classList.contains('dark');

  // State for loading indicators
  const [loading, setLoading] = useState({
    clearConversation: false,
    saveVectorStore: false,
    resetSystem: false,
    processDocuments: false
  });

  // State for success/error messages
  const [message, setMessage] = useState({
    type: null, // 'success' or 'error'
    text: ''
  });

  // Show message function
  const showMessage = (type, text, duration = 5000) => {
    setMessage({ type, text });

    // Clear message after duration
    setTimeout(() => {
      setMessage({ type: null, text: '' });
    }, duration);
  };

  // Clear conversation history
  const handleClearConversation = async () => {
    if (!window.confirm('Are you sure you want to clear all conversation history?')) return;

    setLoading({ ...loading, clearConversation: true });

    try {
      const result = await settingsAPI.clearConversation();
      console.log('Conversation cleared:', result);

      showMessage('success', 'Conversation history cleared successfully!');
    } catch (error) {
      console.error('Error clearing conversation:', error);
      showMessage('error', `Failed to clear conversation: ${error.message || error}`);
    } finally {
      setLoading({ ...loading, clearConversation: false });
    }
  };

  // Save vector store
  const handleSaveVectorStore = async () => {
    setLoading({ ...loading, saveVectorStore: true });

    try {
      const result = await settingsAPI.saveVectorStore();
      console.log('Vector store saved:', result);

      showMessage('success', 'Vector store saved successfully!');
    } catch (error) {
      console.error('Error saving vector store:', error);
      showMessage('error', `Failed to save vector store: ${error.message || error}`);
    } finally {
      setLoading({ ...loading, saveVectorStore: false });
    }
  };

  // Process all documents
  const handleProcessDocuments = async () => {
    setLoading({ ...loading, processDocuments: true });

    try {
      const result = await settingsAPI.processDataFolder();
      console.log('Documents processed:', result);

      showMessage('success', 'Documents processed successfully!');
    } catch (error) {
      console.error('Error processing documents:', error);
      showMessage('error', `Failed to process documents: ${error.message || error}`);
    } finally {
      setLoading({ ...loading, processDocuments: false });
    }
  };

  // Reset system (hard reset)
  const handleResetSystem = async () => {
    if (!window.confirm('WARNING: This will delete all documents and embeddings. This action cannot be undone. Are you sure?')) return;
    if (!window.confirm('FINAL WARNING: All data will be permanently deleted. Continue?')) return;

    setLoading({ ...loading, resetSystem: true });

    try {
      const result = await settingsAPI.hardReset();
      console.log('Hard reset result:', result);

      if (result.status === 'success') {
        // Create a detailed message with the results
        const details = result.details || {};
        const detailsMessage = `
          Documents deleted: ${details.documents_deleted || 0}
          Embeddings deleted: ${details.embeddings_deleted || 0}
          Document access records deleted: ${details.document_access_deleted || 0}
          Chatbot configs deleted: ${details.chatbot_configs_deleted || 0}
          S3 files deleted: ${details.s3_files_deleted || 0}
          Vector stores deleted: ${details.vector_stores_deleted ? 'Yes' : 'No'}
          User data deleted: ${details.user_data_deleted ? 'Yes' : 'No'}
          Data directory cleaned: ${details.data_dir_cleaned ? 'Yes' : 'No'}
        `;

        // Show success message with details and reload notification
        showMessage('success', `System reset successfully! All documents and embeddings have been removed.\n\nDetails:\n${detailsMessage}\n\nThe page will reload in 3 seconds to complete the reset process.`);

        setTimeout(() => {
          // Force a complete reload from the server, not from cache
          window.location.href = '/admin/dashboard';
        }, 3000);
      } else {
        // Show error details if available
        const errorDetails = result.details?.errors?.join('\n') || '';
        showMessage('error', `Reset completed with issues: ${result.message}\n\n${errorDetails}`);
      }
    } catch (error) {
      console.error('Error resetting system:', error);
      showMessage('error', `Failed to reset system: ${error.message || error}`);
    } finally {
      setLoading({ ...loading, resetSystem: false });
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Page header */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        marginBottom: '0.5rem'
      }} className="md-flex-row md-items-center md-justify-between">
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: '600',
          color: darkMode ? '#F9FAFB' : '#111827'
        }}>Admin Settings</h1>
      </div>

      {/* Status message */}
      {message.text && (
        <div style={{
          padding: '1rem',
          borderRadius: '0.5rem',
          backgroundColor: message.type === 'success'
            ? (darkMode ? '#065F46' : '#D1FAE5')
            : (darkMode ? '#7F1D1D' : '#FEE2E2'),
          color: message.type === 'success'
            ? (darkMode ? '#A7F3D0' : '#047857')
            : (darkMode ? '#FECACA' : '#B91C1C'),
          marginBottom: '1rem',
          whiteSpace: 'pre-line',
          maxHeight: '300px',
          overflowY: 'auto'
        }}>
          {message.text}
        </div>
      )}

      {/* Settings sections */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: '1.5rem'
      }}>
        {/* System Maintenance */}
        <div style={{
          borderRadius: '0.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          backgroundColor: darkMode ? '#374151' : '#FFFFFF'
        }}>
          <div style={{
            padding: '1rem 1.5rem',
            borderBottom: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
          }}>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '500',
              color: darkMode ? '#F9FAFB' : '#111827'
            }}>System Maintenance</h3>
            <p style={{
              marginTop: '0.25rem',
              fontSize: '0.875rem',
              color: darkMode ? '#9CA3AF' : '#6B7280'
            }}>
              Manage system data and perform maintenance operations.
            </p>
          </div>
          <div style={{ padding: '1rem 1.5rem' }}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '1rem' }} className="md-grid-cols-2">
              {/* Clear Conversation */}
              <div style={{
                padding: '1rem',
                borderRadius: '0.5rem',
                backgroundColor: darkMode ? '#1F2937' : '#F9FAFB',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <ArrowPathIcon style={{
                    height: '1.25rem',
                    width: '1.25rem',
                    color: darkMode ? '#60A5FA' : '#3B82F6',
                    marginRight: '0.5rem'
                  }} />
                  <h4 style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827'
                  }}>Clear Conversation History</h4>
                </div>
                <p style={{
                  fontSize: '0.75rem',
                  color: darkMode ? '#9CA3AF' : '#6B7280',
                  marginBottom: '1rem'
                }}>
                  Clear all conversation history without affecting documents or embeddings.
                </p>
                <button
                  onClick={handleClearConversation}
                  disabled={loading.clearConversation}
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    padding: '0.5rem',
                    border: 'none',
                    borderRadius: '0.375rem',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: loading.clearConversation
                      ? (darkMode ? '#4B5563' : '#9CA3AF')
                      : (darkMode ? '#2563EB' : '#3B82F6'),
                    cursor: loading.clearConversation ? 'not-allowed' : 'pointer'
                  }}
                >
                  {loading.clearConversation ? (
                    <>
                      <ArrowPathIcon style={{
                        height: '1rem',
                        width: '1rem',
                        marginRight: '0.5rem',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Clearing...
                    </>
                  ) : (
                    'Clear Conversations'
                  )}
                </button>
              </div>

              {/* Save Vector Store */}
              <div style={{
                padding: '1rem',
                borderRadius: '0.5rem',
                backgroundColor: darkMode ? '#1F2937' : '#F9FAFB',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <CloudArrowUpIcon style={{
                    height: '1.25rem',
                    width: '1.25rem',
                    color: darkMode ? '#60A5FA' : '#3B82F6',
                    marginRight: '0.5rem'
                  }} />
                  <h4 style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827'
                  }}>Save Vector Store</h4>
                </div>
                <p style={{
                  fontSize: '0.75rem',
                  color: darkMode ? '#9CA3AF' : '#6B7280',
                  marginBottom: '1rem'
                }}>
                  Save the current vector store to disk for persistence.
                </p>
                <button
                  onClick={handleSaveVectorStore}
                  disabled={loading.saveVectorStore}
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    padding: '0.5rem',
                    border: 'none',
                    borderRadius: '0.375rem',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: loading.saveVectorStore
                      ? (darkMode ? '#4B5563' : '#9CA3AF')
                      : (darkMode ? '#2563EB' : '#3B82F6'),
                    cursor: loading.saveVectorStore ? 'not-allowed' : 'pointer'
                  }}
                >
                  {loading.saveVectorStore ? (
                    <>
                      <ArrowPathIcon style={{
                        height: '1rem',
                        width: '1rem',
                        marginRight: '0.5rem',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Saving...
                    </>
                  ) : (
                    'Save Vector Store'
                  )}
                </button>
              </div>

              {/* Process Documents */}
              <div style={{
                padding: '1rem',
                borderRadius: '0.5rem',
                backgroundColor: darkMode ? '#1F2937' : '#F9FAFB',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <DocumentTextIcon style={{
                    height: '1.25rem',
                    width: '1.25rem',
                    color: darkMode ? '#60A5FA' : '#3B82F6',
                    marginRight: '0.5rem'
                  }} />
                  <h4 style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827'
                  }}>Process All Documents</h4>
                </div>
                <p style={{
                  fontSize: '0.75rem',
                  color: darkMode ? '#9CA3AF' : '#6B7280',
                  marginBottom: '1rem'
                }}>
                  Process all documents in the data folder and update embeddings.
                </p>
                <button
                  onClick={handleProcessDocuments}
                  disabled={loading.processDocuments}
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    padding: '0.5rem',
                    border: 'none',
                    borderRadius: '0.375rem',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: loading.processDocuments
                      ? (darkMode ? '#4B5563' : '#9CA3AF')
                      : (darkMode ? '#2563EB' : '#3B82F6'),
                    cursor: loading.processDocuments ? 'not-allowed' : 'pointer'
                  }}
                >
                  {loading.processDocuments ? (
                    <>
                      <ArrowPathIcon style={{
                        height: '1rem',
                        width: '1rem',
                        marginRight: '0.5rem',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Processing...
                    </>
                  ) : (
                    'Process Documents'
                  )}
                </button>
              </div>

              {/* Hard Reset */}
              <div style={{
                padding: '1rem',
                borderRadius: '0.5rem',
                backgroundColor: darkMode ? '#7F1D1D' : '#FEE2E2',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <ExclamationTriangleIcon style={{
                    height: '1.25rem',
                    width: '1.25rem',
                    color: darkMode ? '#FECACA' : '#DC2626',
                    marginRight: '0.5rem'
                  }} />
                  <h4 style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#FECACA' : '#B91C1C'
                  }}>Hard Reset</h4>
                </div>
                <p style={{
                  fontSize: '0.75rem',
                  color: darkMode ? '#FECACA' : '#B91C1C',
                  marginBottom: '1rem'
                }}>
                  WARNING: This will delete all documents and embeddings. This action cannot be undone.
                </p>
                <button
                  onClick={handleResetSystem}
                  disabled={loading.resetSystem}
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    padding: '0.5rem',
                    border: 'none',
                    borderRadius: '0.375rem',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: loading.resetSystem
                      ? (darkMode ? '#4B5563' : '#9CA3AF')
                      : (darkMode ? '#DC2626' : '#EF4444'),
                    cursor: loading.resetSystem ? 'not-allowed' : 'pointer'
                  }}
                >
                  {loading.resetSystem ? (
                    <>
                      <ArrowPathIcon style={{
                        height: '1rem',
                        width: '1rem',
                        marginRight: '0.5rem',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Resetting...
                    </>
                  ) : (
                    <>
                      <TrashIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                      Reset System
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
