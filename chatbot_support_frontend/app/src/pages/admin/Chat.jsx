import React, { useState, useEffect, useRef } from 'react';
import {
  PaperAirplaneIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  ExclamationCircleIcon,
  ChatBubbleLeftRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { useChat } from '../../contexts/ChatContext';
import MarkdownRenderer from '../../components/common/MarkdownRenderer';
import ChatHistory from '../../components/chat/ChatHistory';
import '../user/UserChat.css';
import './AdminChat.css';

const Chat = () => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showHistoryDropdown, setShowHistoryDropdown] = useState(false);
  const messagesEndRef = useRef(null);
  const dropdownRef = useRef(null);

  // Get chat context
  const {
    messages,
    loading,
    error,
    initialized,
    sendMessage,
    clearChat,
    initializeChat,
    formatTimestamp,
    startNewConversation
  } = useChat();

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (message.trim() === '' || loading) return;

    const userMessage = message;
    setMessage('');
    setIsTyping(true);

    try {
      await sendMessage(userMessage);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowHistoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Function to get a clean document name (without extension)
  const getDocumentName = (filename) => {
    if (!filename) return "Unknown Document";

    // Remove file extension
    const nameParts = filename.split('.');
    if (nameParts.length > 1) {
      nameParts.pop(); // Remove the last part (extension)
    }

    // Join back and capitalize words
    return nameParts.join('.')
      .split(/[_-]/) // Split by underscores or hyphens
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <div className="modern-chat-container admin-page" style={{ height: 'calc(100vh - 65px)' }}>
      <div className="modern-chat-main">
        {/* Chat area */}
        <div className="modern-chat-content">
          {/* Chat header */}
          <div className="modern-chat-header">
            <div className="modern-chat-header-left">
              <div className="modern-ai-avatar">
                <span>AI</span>
              </div>

              <div className="modern-ai-info">
                <h3 className="modern-ai-name">Document AI Assistant</h3>
                <div className="modern-ai-status">
                  <div className={`modern-status-indicator ${initialized ? 'ready' : 'initializing'}`}></div>
                  <p className="modern-status-text">{initialized ? 'Ready' : 'Initializing...'}</p>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="modern-chat-actions">
              {/* New chat button */}
              <button
                onClick={startNewConversation}
                className="modern-action-button"
                title="Start a new chat"
              >
                <ChatBubbleLeftRightIcon style={{ height: '1rem', width: '1rem' }} />
                <span>New Chat</span>
              </button>

              {/* Reset AI button */}
              <button
                onClick={initializeChat}
                className="modern-action-button"
                title="Reinitialize the chatbot"
              >
                <DocumentTextIcon style={{ height: '1rem', width: '1rem' }} />
                <span>Reset AI</span>
              </button>

              {/* Clear chat button */}
              <button
                onClick={clearChat}
                className="modern-action-button"
                title="Clear current chat"
              >
                <ArrowPathIcon style={{ height: '1rem', width: '1rem' }} />
                <span>Clear</span>
              </button>

              {/* Chat History Dropdown */}
              <div className="history-dropdown-container" ref={dropdownRef}>
                <button
                  onClick={() => setShowHistoryDropdown(!showHistoryDropdown)}
                  className="modern-action-button"
                  title="Show chat history"
                >
                  <ChatBubbleLeftRightIcon style={{ height: '1rem', width: '1rem' }} />
                  <span>History</span>
                  <ChevronDownIcon style={{ height: '0.75rem', width: '0.75rem', marginLeft: '0.25rem' }} />
                </button>

                {showHistoryDropdown && (
                  <div className="history-dropdown-content">
                    <ChatHistory />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="modern-chat-messages">
            <div className="modern-message-list">
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`modern-message-container ${msg.sender}`}
                >
                  <div className={`modern-message-bubble ${msg.sender}`}>
                    {msg.sender === 'bot' && msg.format === 'markdown' ? (
                      <>
                        <div className="modern-message-content">
                          <MarkdownRenderer
                            content={msg.text}
                            style={{ whiteSpace: 'pre-wrap' }}
                          />
                        </div>
                        {msg.document_references && msg.document_references.length > 0 && (
                          <div className="modern-document-references">
                            <div className="modern-references-title">Sources:</div>
                            <div className="modern-references-list">
                              {msg.document_references.map((ref, index) => (
                                <div key={index} className="modern-reference-item">
                                  <a
                                    href={ref.url || '#'}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="modern-reference-link"
                                    onClick={(e) => {
                                      if (!ref.url) {
                                        e.preventDefault();
                                        console.log('Document URL not available:', ref);
                                        alert(`Document URL not available for: ${ref.filename}`);
                                      }
                                    }}
                                  >
                                    {getDocumentName(ref.filename) || `Document ${index + 1}`}
                                    {ref.page && <span> (Page {ref.page})</span>}
                                  </a>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="modern-message-content">
                        {msg.text}
                      </div>
                    )}
                    <div className="modern-message-timestamp">
                      {formatTimestamp(msg.timestamp)}
                    </div>
                  </div>
                </div>
              ))}

              {/* Typing indicator */}
              {isTyping && (
                <div className="modern-typing-indicator">
                  <div className="modern-typing-bubble">
                    <div className="modern-typing-dots">
                      <div className="modern-typing-dot"></div>
                      <div className="modern-typing-dot"></div>
                      <div className="modern-typing-dot"></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error message */}
              {error && (
                <div className="modern-error-container">
                  <div className="modern-error-message">
                    <ExclamationCircleIcon style={{ height: '1rem', width: '1rem' }} />
                    {error}
                  </div>
                </div>
              )}

              {/* Scroll anchor */}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Message input */}
          <div className="modern-chat-input-container" style={{ padding: '0.4rem 1rem' }}>
            <form onSubmit={handleSendMessage} className="modern-chat-input-form">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={loading || !initialized}
                className="modern-chat-input"
                placeholder={initialized ? "Ask a question about your documents..." : "Initializing AI..."}
                rows="1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
                style={{
                  height: message.split('\n').length > 3 ? 'auto' : '2.2rem',
                  padding: '0.4rem'
                }}
              />
              <button
                type="submit"
                disabled={loading || !initialized || message.trim() === ''}
                className="modern-chat-send-button"
                style={{ padding: '0.4rem' }}
              >
                <PaperAirplaneIcon className="modern-send-icon" />
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chat;
