import React from 'react';
import NotificationReadStatus from '../../components/admin/notifications/NotificationReadStatus';
import { useTheme } from '../../contexts/ThemeContext';

const Dashboard = () => {
  const theme = useTheme();

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Page header */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
      }} className="md-flex-row md-items-center md-justify-between">
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: '600',
          color: theme.colors.text.primary
        }}>Dashboard</h1>
      </div>

      {/* Notification Read Status Component */}
      <div style={{
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        backgroundColor: theme.colors.background.secondary
      }}>
        <NotificationReadStatus />
      </div>
    </div>
  );
};

export default Dashboard;
