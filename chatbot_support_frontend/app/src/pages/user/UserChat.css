/* Chat container styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden; /* Prevent scrolling on the container */
}

/* Chat main area */
.chat-main {
  flex: 1;
  display: flex;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100vh - 80px); /* Fixed height for the main chat area */
}

/* Admin specific container */
.admin-page .chat-container {
  overflow: visible; /* Allow scrolling on admin container */
}

.admin-page .chat-main {
  overflow: visible; /* Allow scrolling on admin main area */
  height: auto; /* Auto height for admin */
  min-height: calc(100vh - 140px); /* Minimum height for admin */
}

/* Chat sidebar */
.chat-sidebar {
  height: 100%;
  transition: all 0.3s ease;
  width: 280px;
  min-width: 280px;
  max-width: 350px;
  overflow: hidden;
  border-right: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.chat-sidebar.expanded {
  width: 350px;
  min-width: 350px;
}

/* Admin specific sidebar */
.admin-page .chat-sidebar {
  height: 100vh; /* Full viewport height */
  position: sticky;
  top: 0;
  overflow-y: auto; /* Allow scrolling in sidebar */
  overflow-x: hidden;
  max-height: calc(100vh - 20px); /* Prevent overflow */
}

/* Chat content area */
.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  min-width: 0;
  height: 100%; /* Ensure full height */
  overflow: hidden; /* Prevent scrolling on the container */
}

/* Admin specific content area */
.admin-page .chat-content {
  height: auto; /* Auto height for admin */
  min-height: calc(100vh - 140px); /* Minimum height for admin */
  overflow: visible; /* Allow content to be visible */
}

/* Chat header */
.chat-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Chat messages area */
.chat-messages {
  flex: 1;
  padding: 0.75rem 1rem;
  overflow-y: auto;
  scrollbar-width: thin;
  max-height: calc(100vh - 160px); /* Fixed height for scrolling */
  height: 100%;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: var(--text-secondary);
  border-radius: 3px;
  opacity: 0.7;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-primary);
  opacity: 1;
}

/* Admin specific messages area */
.admin-page .chat-messages {
  max-height: none; /* Remove max height for admin */
  height: auto; /* Auto height for admin */
  overflow: visible; /* Allow content to be visible */
}

/* Message groups */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 100%;
  padding-bottom: 0.5rem; /* Add padding at the bottom for better spacing */
}

/* Empty state */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 2rem;
  color: var(--text-secondary);
  text-align: center;
}

/* Message input area */
.chat-input-container {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
}

.chat-input-form {
  display: flex;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
}

.chat-send-button {
  margin-left: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark mode specific styles */
.dark .chat-input {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.dark .chat-send-button {
  background-color: var(--accent-color);
  color: white;
}

.dark .chat-send-button:disabled {
  background-color: var(--text-secondary);
  cursor: not-allowed;
}

/* Responsive styles */
@media (max-width: 768px) {
  .chat-sidebar {
    position: absolute;
    z-index: 10;
    height: 100%;
    background-color: var(--bg-primary);
    width: 280px;
  }
}

@media (min-width: 1024px) {
  .chat-main {
    height: calc(100vh - 80px);
  }

  .chat-messages {
    max-height: calc(100vh - 160px);
  }
}

/* Admin specific styles */
.admin-page .chat-main {
  height: calc(100vh - 120px);
}

.admin-page .chat-messages {
  max-height: calc(100vh - 200px);
}
