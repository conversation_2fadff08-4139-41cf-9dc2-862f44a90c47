// Centralized API Configuration
// This is the single source of truth for all API URLs in the application

class APIConfiguration {
  constructor() {
    this.baseURL = this.initializeBaseURL();
    this.isInitialized = true;
    
    // Log the final configuration
    console.log('🔧 API Configuration:', {
      baseURL: this.baseURL,
      source: this.getConfigSource()
    });
  }

  initializeBaseURL() {
    // Priority order for API URL configuration:
    // 1. window._env_.REACT_APP_API_URL (from Docker environment injection)
    // 2. Default production URL
    
    try {
      if (window._env_ && window._env_.REACT_APP_API_URL) {
        console.log('✅ Using API URL from environment configuration');
        return window._env_.REACT_APP_API_URL;
      }
    } catch (error) {
      console.warn('⚠️ Error accessing window._env_:', error);
    }
    
    // Default fallback URL
    console.log('📍 Using default API URL');
    return 'http://support-chatbot-backend-dev.jmscpos.online';
  }

  getConfigSource() {
    if (window._env_ && window._env_.REACT_APP_API_URL) {
      return 'environment';
    }
    return 'default';
  }

  // Get the base URL for API calls
  getBaseURL() {
    return this.baseURL;
  }

  // Build full URL for an endpoint
  buildURL(endpoint) {
    // Remove leading slash if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.baseURL}/${cleanEndpoint}`;
  }

  // Check if configuration is ready
  isReady() {
    return this.isInitialized && this.baseURL;
  }

  // Get configuration info for debugging
  getInfo() {
    return {
      baseURL: this.baseURL,
      source: this.getConfigSource(),
      isReady: this.isReady(),
      timestamp: new Date().toISOString()
    };
  }
}

// Create and export single instance
const apiConfig = new APIConfiguration();

// Export the instance and commonly used methods
export default apiConfig;
export const getAPIBaseURL = () => apiConfig.getBaseURL();
export const buildAPIURL = (endpoint) => apiConfig.buildURL(endpoint);
export const getAPIInfo = () => apiConfig.getInfo();

// For backward compatibility
export const API_BASE_URL = apiConfig.getBaseURL();
