import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import { useAuth } from './AuthContext';
import { documentAPI } from '../services/api';
import { useNotification } from './NotificationContext';

// Create document context
const DocumentContext = createContext();

// Document provider component
export const DocumentProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const { checkForNewNotifications } = useNotification();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const isFetchingRef = useRef(false);

  // Fetch documents when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchDocuments();
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  // Fetch all documents
  const fetchDocuments = async () => {
    // Prevent multiple simultaneous API calls
    if (isFetchingRef.current) {
      console.log('Already fetching documents, skipping duplicate request');
      return { success: true, data: documents };
    }

    try {
      isFetchingRef.current = true;
      setLoading(true);
      setError(null);

      const response = await documentAPI.getAllDocuments();
      console.log('Documents API response:', response);

      // The document-status endpoint returns a different format
      let documentData = [];

      if (response.documents && Array.isArray(response.documents)) {
        // If documents property exists and is an array, use it
        documentData = response.documents.map(doc => ({
          id: doc.id || doc.doc_id,
          document_number: doc.document_number || '',  // Add document number
          filename: doc.filename,
          name: doc.filename,
          size: doc.size || 'Unknown',
          type: doc.content_type || 'Document',
          uploadDate: doc.uploaded_at || new Date().toISOString().split('T')[0],
          status: doc.processed ? 'Active' : 'Processing',
          groups: doc.groups || [],
          uploadedBy: doc.uploaded_by || '',
          uploaderName: doc.uploader_name || 'Unknown',
          // Add the new metadata fields
          service_name: doc.service_name || '',
          software_menus: doc.software_menus || '',
          issue_type: doc.issue_type || ''
        }));
      } else if (response.documents && typeof response.documents === 'object') {
        // If documents is an object with document data, transform it to array
        documentData = Object.keys(response.documents).map(key => {
          const doc = response.documents[key];
          return {
            id: doc.id || doc.doc_id || key,
            document_number: doc.document_number || '',  // Add document number
            filename: doc.filename,
            name: doc.filename,
            size: doc.size || 'Unknown',
            type: doc.content_type || 'Document',
            uploadDate: doc.uploaded_at || new Date().toISOString().split('T')[0],
            status: doc.processed ? 'Active' : 'Processing',
            groups: doc.groups || [],
            uploadedBy: doc.uploaded_by || '',
            uploaderName: doc.uploader_name || 'Unknown',
            // Add the new metadata fields
            service_name: doc.service_name || '',
            software_menus: doc.software_menus || '',
            issue_type: doc.issue_type || ''
          };
        });
      } else if (Array.isArray(response)) {
        // If response is an array, use it directly with transformation
        documentData = response.map(doc => ({
          id: doc.id || doc.doc_id,
          document_number: doc.document_number || '',  // Add document number
          filename: doc.filename,
          name: doc.filename,
          size: doc.size || 'Unknown',
          type: doc.content_type || 'Document',
          uploadDate: doc.uploaded_at || new Date().toISOString().split('T')[0],
          status: doc.processed ? 'Active' : 'Processing',
          groups: doc.groups || [],
          uploadedBy: doc.uploaded_by || '',
          uploaderName: doc.uploader_name || 'Unknown',
          // Add the new metadata fields
          service_name: doc.service_name || '',
          software_menus: doc.software_menus || '',
          issue_type: doc.issue_type || ''
        }));
      } else if (typeof response === 'object') {
        // If response is a direct object of documents
        documentData = Object.keys(response).map(key => {
          const doc = response[key];
          if (!doc) return null; // Skip if doc is null or undefined

          return {
            id: doc.id || doc.doc_id || key,
            document_number: doc.document_number || '',  // Add document number
            filename: doc.filename,
            name: doc.filename,
            size: doc.size || 'Unknown',
            type: doc.content_type || 'Document',
            uploadDate: doc.uploaded_at || new Date().toISOString().split('T')[0],
            status: doc.processed ? 'Active' : 'Processing',
            groups: doc.groups || [],
            uploadedBy: doc.uploaded_by || '',
            uploaderName: doc.uploader_name || 'Unknown',
            // Add the new metadata fields
            service_name: doc.service_name || '',
            software_menus: doc.software_menus || '',
            issue_type: doc.issue_type || ''
          };
        }).filter(Boolean); // Remove null entries
      }

      console.log('Transformed document data:', documentData);
      setDocuments(documentData);

      return { success: true, data: documentData };
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('Failed to fetch documents: ' + (error.message || error));
      return { success: false, error: error.message || error };
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  };

  // Upload a document
  const uploadDocument = async (formData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await documentAPI.uploadDocument(formData);

      // Refresh document list to get the latest documents including the new one
      const refreshResult = await fetchDocuments();

      // Find the newly uploaded document to get its document number
      let documentNumber = '';
      if (refreshResult.success && refreshResult.data && refreshResult.data.length > 0) {
        // Try to find the document by ID if available
        if (response.document_id) {
          const newDoc = refreshResult.data.find(doc => doc.id === response.document_id);
          if (newDoc && newDoc.document_number) {
            documentNumber = newDoc.document_number;
          }
        }

        // If we couldn't find it by ID, try to find the most recently uploaded document
        if (!documentNumber) {
          // Sort by upload date (newest first) and take the first one
          const sortedDocs = [...refreshResult.data].sort((a, b) =>
            new Date(b.uploadDate) - new Date(a.uploadDate)
          );
          if (sortedDocs.length > 0 && sortedDocs[0].document_number) {
            documentNumber = sortedDocs[0].document_number;
          }
        }
      }

      // Check for notifications after document upload
      setTimeout(() => {
        checkForNewNotifications();
      }, 1000);

      return {
        success: true,
        documentId: response.document_id,
        documentNumber: documentNumber
      };
    } catch (error) {
      console.error('Error uploading document:', error);

      // Check for specific Google API error
      if (error.message && (
          error.message.includes('Google API key or service account file must be provided') ||
          error.message.includes('Google API credentials are missing') ||
          error.message.includes('Google API credentials are not properly configured')
      )) {
        const errorMessage = 'The document has been stored in the system, but cannot be processed for AI search because Google API credentials are not properly configured. You can still view and manage the document, but it will not be available for AI-powered search until the administrator configures Google API credentials.';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      // Get the error message from the response if available
      let errorMessage = 'Failed to upload document';
      if (error.response && error.response.data && error.response.data.detail) {
        errorMessage = error.response.data.detail;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Delete a document
  const deleteDocument = async (documentId) => {
    try {
      setLoading(true);
      setError(null);

      try {
        await documentAPI.deleteDocument(documentId);
      } catch (apiError) {
        // If the document is not found (404), consider it a success
        // This handles cases where the document was already deleted
        if (apiError.status === 404) {
          console.log('Document not found (already deleted):', documentId);
          // Continue with the success path
        } else {
          // For other errors, rethrow to be caught by the outer catch
          throw apiError;
        }
      }

      // Refresh document list
      await fetchDocuments();

      // Check for notifications after document deletion
      setTimeout(() => {
        checkForNewNotifications();
      }, 1000);

      return { success: true };
    } catch (error) {
      console.error('Error deleting document:', error);
      setError('Failed to delete document');
      return {
        success: false,
        error: error.message || (typeof error === 'string' ? error : 'Failed to delete document')
      };
    } finally {
      setLoading(false);
    }
  };

  // Set document access
  const setDocumentAccess = async (documentId, groupIds) => {
    try {
      setLoading(true);
      setError(null);

      await documentAPI.setDocumentAccess(documentId, groupIds);

      // Refresh document list
      await fetchDocuments();

      return { success: true };
    } catch (error) {
      console.error('Error setting document access:', error);
      setError('Failed to set document access');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to set document access' };
    } finally {
      setLoading(false);
    }
  };

  // Get document URL
  const getDocumentUrl = async (documentId, expiration = 3600) => {
    try {
      setLoading(true);
      setError(null);

      const response = await documentAPI.getDocumentUrl(documentId, expiration);

      return {
        success: true,
        url: response.url,
        filename: response.filename,
        document_id: response.document_id
      };
    } catch (error) {
      console.error('Error getting document URL:', error);
      setError('Failed to get document URL');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to get document URL' };
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    documents,
    loading,
    error,
    fetchDocuments,
    uploadDocument,
    deleteDocument,
    setDocumentAccess,
    getDocumentUrl,
  };

  return (
    <DocumentContext.Provider value={value}>
      {children}
    </DocumentContext.Provider>
  );
};

// Custom hook to use document context
export const useDocument = () => {
  const context = useContext(DocumentContext);

  if (!context) {
    throw new Error('useDocument must be used within a DocumentProvider');
  }

  return context;
};
