import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { chatAPI } from '../services/api';
import { settingsAPI } from '../services/settingsAPI';

// Create chat context
const ChatContext = createContext();

// Chat provider component
export const ChatProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [initialized, setInitialized] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);

  // Initialize chat when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Instead of initializing the chatbot, just load conversations
      // The backend will auto-initialize the chatbot when needed
      fetchConversations();

      // Add welcome message
      setMessages([
        {
          id: 'system-welcome',
          sender: 'system',
          text: 'Welcome to the Document AI Chat! I can answer questions based on your uploaded documents.',
          timestamp: new Date().toISOString()
        }
      ]);

      // Try to load the most recent conversation
      loadMostRecentConversation();

      // Mark as initialized
      setInitialized(true);
    }
  }, [isAuthenticated]);

  // Load most recent conversation
  const loadMostRecentConversation = async () => {
    try {
      const recentConversationResponse = await chatAPI.getMostRecentConversation();
      console.log('Recent conversation response:', recentConversationResponse);

      if (recentConversationResponse.conversation) {
        const mostRecentConversation = recentConversationResponse.conversation;
        console.log('Most recent conversation:', mostRecentConversation);

        // Set current conversation ID
        setCurrentConversationId(mostRecentConversation.id);

        // Load the conversation messages
        await loadConversationMessages(mostRecentConversation.id);
      }
    } catch (error) {
      console.error('Error loading most recent conversation:', error);
      // Continue even if we can't load conversation history
    }
  };

  // Fetch all conversations with pagination support
  const fetchConversations = async (page = 1, limit = 20, includeEmpty = false) => {
    try {
      // Use settingsAPI instead of chatAPI to get conversations
      const response = await settingsAPI.getConversations(page, limit, includeEmpty);
      console.log('Conversations:', response);
      if (response.conversations) {
        if (page === 1) {
          // Replace all conversations on first page
          setConversations(response.conversations);
        } else {
          // Append conversations for subsequent pages
          setConversations(prev => [...prev, ...response.conversations]);
        }
      }
      return response.conversations || [];
    } catch (error) {
      console.error('Error fetching conversations:', error);
      return [];
    }
  };

  // Initialize chat
  const initializeChat = async () => {
    try {
      setLoading(true);
      setError(null);

      // Add welcome message
      setMessages([
        {
          id: 'system-welcome',
          sender: 'system',
          text: 'Welcome to the Document AI Chat! I can answer questions based on your uploaded documents.',
          timestamp: new Date().toISOString()
        }
      ]);

      // Get Google project ID from backend
      let projectId = null;
      try {
        const projectResponse = await chatAPI.getGoogleProjectId();
        projectId = projectResponse.project_id;
        console.log('Using Google project ID from backend:', projectId);
      } catch (error) {
        console.error('Error getting Google project ID:', error);
        // Continue with initialization even if we can't get the project ID
      }

      // Initialize chatbot with Google AI settings
      const config = {
        use_openai_embeddings: false,
        use_google_embeddings: true,
        use_google_llm: true,
        google_project_id: projectId,
        embedding_model: "all-MiniLM-L6-v2",
        llm_model_name: "gemini-pro",
        vector_store_type: "chroma",
        use_compression: false,
        top_k: 4,
        max_memory_messages: 10
      };

      const result = await chatAPI.initializeChatbot(config);
      console.log('Chatbot initialized:', result);

      // Try to load conversation history
      try {
        // Get the most recent conversation
        const recentConversationResponse = await chatAPI.getMostRecentConversation();
        console.log('Recent conversation response:', recentConversationResponse);

        if (recentConversationResponse.conversation) {
          const mostRecentConversation = recentConversationResponse.conversation;
          console.log('Most recent conversation:', mostRecentConversation);

          // Set current conversation ID
          setCurrentConversationId(mostRecentConversation.id);

          // Load the conversation messages
          await loadConversationMessages(mostRecentConversation.id);
        } else {
          // No previous conversation, set default welcome message
          setMessages([
            {
              id: 'system-welcome',
              sender: 'system',
              text: 'Welcome to Document AI Chat! Ask me anything about your documents.',
              timestamp: new Date().toISOString()
            }
          ]);
        }
      } catch (historyError) {
        console.error('Error loading conversation history:', historyError);
        // Continue with initialization even if we can't load conversation history
      }

      setInitialized(true);

      return { success: true };
    } catch (error) {
      console.error('Error initializing chat:', error);
      setError('Failed to initialize chat. Please try again.');

      // Add error message
      setMessages(prev => [
        ...prev,
        {
          id: `system-error-${Date.now()}`,
          sender: 'system',
          text: `Error: ${typeof error === 'string' ? error : 'Failed to initialize chat. Please try again.'}`,
          timestamp: new Date().toISOString()
        }
      ]);

      return { success: false, error: typeof error === 'string' ? error : 'Failed to initialize chat' };
    } finally {
      setLoading(false);
    }
  };

  // Send message
  const sendMessage = async (messageText) => {
    try {
      setLoading(true);
      setError(null);

      // Add user message to state
      const userMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        text: messageText,
        timestamp: new Date().toISOString(),
        user_id: user?.id // Include user_id from auth context
      };

      setMessages(prev => [...prev, userMessage]);

      // If this is a new conversation (no conversation ID), create one with the message as title
      if (!currentConversationId) {
        // Create a title from the first message (truncate if too long)
        const title = messageText.length > 50 ? messageText.substring(0, 47) + '...' : messageText;

        try {
          // Create a new conversation with this title
          const newConversation = await settingsAPI.createNewConversation(title);
          if (newConversation && newConversation.conversation) {
            setCurrentConversationId(newConversation.conversation.id);
            console.log(`Created new conversation with ID: ${newConversation.conversation.id}`);
          }
        } catch (error) {
          console.error('Error creating new conversation:', error);
        }
      }

      // Send message to API
      const response = await chatAPI.sendMessage(messageText);

      // Log the full response for debugging
      console.log('Chat API response:', response);

      // Add bot response to state
      const botMessage = {
        id: `bot-${Date.now()}`,
        sender: 'bot',
        text: response.response,
        timestamp: new Date().toISOString(),
        user_id: null, // Bot messages don't have a user_id
        format: response.format || 'markdown', // Get format from response or default to markdown
        document_references: response.document_references || [] // Include document references if available
      };

      // Log the bot message for debugging
      console.log('Bot message:', botMessage);

      setMessages(prev => [...prev, botMessage]);

      // Refresh conversations list after sending a message
      fetchConversations();

      return { success: true, response: response.response };
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');

      // Add error message
      setMessages(prev => [
        ...prev,
        {
          id: `system-error-${Date.now()}`,
          sender: 'system',
          text: `Error: ${typeof error === 'string' ? error : 'Failed to send message. Please try again.'}`,
          timestamp: new Date().toISOString(),
          user_id: null // System messages don't have a user_id
        }
      ]);

      return { success: false, error: typeof error === 'string' ? error : 'Failed to send message' };
    } finally {
      setLoading(false);
    }
  };

  // Clear chat history
  const clearChat = () => {
    setMessages([
      {
        id: 'system-welcome',
        sender: 'system',
        text: 'Chat history cleared. How can I help you today?',
        timestamp: new Date().toISOString()
      }
    ]);
  };

  // Format timestamp with real-time date
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();

    // Check if the date is today
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // Check if the date is yesterday
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Check if the date is within the last 7 days
    const lastWeek = new Date(now);
    lastWeek.setDate(now.getDate() - 7);
    if (date > lastWeek) {
      const options = { weekday: 'short' };
      return `${date.toLocaleDateString(undefined, options)}, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // For older dates, show the full date
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Load conversation messages
  const loadConversationMessages = async (conversationId) => {
    try {
      setLoading(true);

      // Get messages for this conversation
      const messagesResponse = await chatAPI.getConversationMessages(conversationId);
      console.log('Loaded conversation messages:', messagesResponse);

      if (messagesResponse.messages && messagesResponse.messages.length > 0) {
        // Convert messages to the format expected by the UI
        const formattedMessages = messagesResponse.messages.map(msg => ({
          id: `${msg.role}-${msg.id || new Date(msg.created_at).getTime()}`, // Use message ID if available
          sender: msg.role,
          text: msg.content,
          timestamp: msg.created_at,
          user_id: msg.user_id,
          format: 'markdown'
        }));

        // Set messages
        setMessages([
          {
            id: 'system-welcome',
            sender: 'system',
            text: 'Loaded previous conversation.',
            timestamp: new Date().toISOString()
          },
          ...formattedMessages
        ]);

        // Set current conversation ID
        setCurrentConversationId(conversationId);

        // Log the number of messages loaded
        console.log(`Loaded ${formattedMessages.length} messages for conversation ${conversationId}`);

        return true;
      } else {
        // No messages in the conversation
        setMessages([
          {
            id: 'system-welcome',
            sender: 'system',
            text: 'This conversation has no messages yet. Start chatting!',
            timestamp: new Date().toISOString()
          }
        ]);

        return false;
      }
    } catch (error) {
      console.error('Error loading conversation messages:', error);
      setError('Failed to load conversation messages');

      // Add error message
      setMessages([
        {
          id: `system-error-${Date.now()}`,
          sender: 'system',
          text: `Error: ${typeof error === 'string' ? error : 'Failed to load conversation messages. Please try again.'}`,
          timestamp: new Date().toISOString()
        }
      ]);

      return false;
    } finally {
      setLoading(false);
    }
  };

  // Load a specific conversation
  const loadConversation = async (conversationId) => {
    if (currentConversationId === conversationId) return;

    await loadConversationMessages(conversationId);
  };

  // Start a new conversation
  const startNewConversation = async () => {
    try {
      setLoading(true);

      // Clear current messages
      setMessages([
        {
          id: 'system-welcome',
          sender: 'system',
          text: 'Starting a new conversation. Ask me anything about your documents!',
          timestamp: new Date().toISOString()
        }
      ]);

      // Clear current conversation ID
      setCurrentConversationId(null);

      try {
        // Create a new conversation on the server
        const response = await settingsAPI.createNewConversation();
        console.log('New conversation created:', response);

        if (response && response.conversation) {
          // Set the new conversation ID
          setCurrentConversationId(response.conversation.id);
        } else {
          throw new Error('Invalid response from server');
        }
      } catch (error) {
        console.error('Error creating new conversation:', error);

        // Add error message to chat
        setMessages(prev => [
          ...prev,
          {
            id: `system-error-${Date.now()}`,
            sender: 'system',
            text: `Failed to create new conversation: ${error.message || 'Unknown error'}. Please try again later.`,
            timestamp: new Date().toISOString()
          }
        ]);

        setError(`Failed to create new conversation: ${error.message || 'Unknown error'}`);

        // Don't try to clear conversation or fetch conversations if we failed to create one
        return false;
      }

      // Clear the current conversation
      await settingsAPI.clearConversation();

      // Refresh conversations list
      await fetchConversations();

      return true;
    } catch (error) {
      console.error('Error starting new conversation:', error);
      setError('Failed to start new conversation');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Delete a conversation
  const deleteConversation = async (conversationId) => {
    try {
      setLoading(true);

      // Delete the conversation
      await settingsAPI.deleteConversation(conversationId);

      // If this was the current conversation, start a new one
      if (currentConversationId === conversationId) {
        await startNewConversation();
      }

      // Refresh the conversations list
      await fetchConversations();

      return true;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      setError('Failed to delete conversation');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    messages,
    loading,
    error,
    initialized,
    conversations,
    currentConversationId,
    sendMessage,
    clearChat,
    initializeChat,
    formatTimestamp,
    loadConversation,
    startNewConversation,
    fetchConversations,
    deleteConversation
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
