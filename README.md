# Chatbot Support Frontend

Simple Docker setup for the frontend application that connects to your backend container.

## Quick Start

1. **Start your backend** (should be running in Docker)
2. **Start the frontend**:
   ```bash
   # Using docker-compose (recommended)
   sudo docker-compose up --build -d

   # Or manually
   sudo docker build -t chatbot-frontend ./chatbot_support_frontend
   sudo docker run -d --name chatbot-frontend --network chatbot_support_backend_chatbot-network -p 3001:80 --env-file .env chatbot-frontend
   ```

## Configuration

- Frontend runs on: http://localhost:3001
- Backend connects via: http://backend:8000 (container-to-container)
- Environment variables are in `.env` file

## Commands

```bash
# Start with docker-compose
sudo docker-compose up --build -d

# Stop
sudo docker-compose down

# View logs
sudo docker-compose logs -f

# Manual commands
sudo docker stop chatbot-frontend
sudo docker rm chatbot-frontend
sudo docker logs -f chatbot-frontend
```

## Environment Variables

Edit `.env` file to configure:
- `REACT_APP_API_URL`: Backend API URL (default: http://backend:8000)
- `REACT_APP_ENV`: Environment (development/production)
- `REACT_APP_VERSION`: Application version

## Network Setup

The frontend connects to your backend container through the shared Docker network `chatbot_support_backend_chatbot-network`.
